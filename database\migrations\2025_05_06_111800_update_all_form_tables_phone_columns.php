<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get all tables that start with 'form_'
        $tables = DB::select("SHOW TABLES LIKE 'form_%'");
        
        foreach ($tables as $table) {
            $tableName = array_values((array)$table)[0];
            
            // Get all columns for this table
            $columns = DB::select("SHOW COLUMNS FROM `{$tableName}`");
            
            foreach ($columns as $column) {
                $columnName = $column->Field;
                $columnType = $column->Type;
                
                // Check if this is a phone number column (by name) and it's an integer type
                if ((stripos($columnName, 'phone') !== false || stripos($columnName, 'mobile') !== false) && 
                    stripos($columnType, 'int') !== false) {
                    
                    // Change the column type to VARCHAR
                    try {
                        DB::statement("ALTER TABLE `{$tableName}` MODIFY `{$columnName}` VARCHAR(20)");
                        Log::info("Changed column type for {$tableName}.{$columnName} from {$columnType} to VARCHAR(20)");
                    } catch (\Exception $e) {
                        Log::error("Failed to change column type for {$tableName}.{$columnName}: " . $e->getMessage());
                    }
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // We don't want to revert back to integer types
    }
};
