<?php

echo "=== Testing Form Creation via HTTP ===\n\n";

// Test form creation endpoint with different JSON formats
$testCases = [
    'Valid Form Structure' => [
        'form_name' => 'HTTP Test Form Valid',
        'target_user' => 'all_scientists',
        'form_structure' => json_encode([
            [
                'type' => 'text',
                'label' => 'Full Name',
                'required' => true,
                'placeholder' => 'Enter your full name'
            ],
            [
                'type' => 'email',
                'label' => 'Email Address',
                'required' => true,
                'placeholder' => 'Enter your email'
            ]
        ]),
        'last_date' => '2025-12-31'
    ],
    'Double Encoded JSON' => [
        'form_name' => 'HTTP Test Form Double Encoded',
        'target_user' => 'all_scientists',
        'form_structure' => json_encode(json_encode([
            [
                'type' => 'text',
                'label' => 'Project Name',
                'required' => true,
                'placeholder' => 'Enter project name'
            ],
            [
                'type' => 'select',
                'label' => 'Priority',
                'required' => true,
                'options' => ['Low', 'Medium', 'High']
            ]
        ])),
        'last_date' => '2025-12-31'
    ]
];

foreach ($testCases as $testName => $postData) {
    echo "🧪 Testing: {$testName}\n";
    
    $url = 'http://127.0.0.1:8001/super-admin/forms';
    $jsonData = json_encode($postData);
    
    echo "   📤 POST URL: {$url}\n";
    echo "   📤 Data preview: " . substr($jsonData, 0, 100) . "...\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json',
        'X-CSRF-TOKEN: test-token',
        'User-Agent: Mozilla/5.0 (compatible; TestBot/1.0)'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "   📥 HTTP Status: {$httpCode}\n";
    
    if ($error) {
        echo "   ❌ cURL Error: {$error}\n";
    } elseif ($httpCode === 0) {
        echo "   ❌ Connection failed (server not running?)\n";
    } elseif ($httpCode === 200) {
        echo "   ✅ SUCCESS: Form created successfully\n";
        $responseData = json_decode($response, true);
        if (isset($responseData['form']['id'])) {
            echo "   📝 Form ID: " . $responseData['form']['id'] . "\n";
        }
    } elseif ($httpCode === 422) {
        echo "   ❌ VALIDATION ERROR: " . $response . "\n";
        $responseData = json_decode($response, true);
        if (isset($responseData['error']) && strpos($responseData['error'], 'Invalid form structure JSON') !== false) {
            echo "   🎯 This is the error we're trying to fix!\n";
        }
    } elseif ($httpCode === 419) {
        echo "   ⚠️ CSRF token mismatch (expected for this test)\n";
    } else {
        echo "   ⚠️ Unexpected status: {$httpCode}\n";
        echo "   📝 Response: " . substr($response, 0, 200) . "\n";
    }
    
    echo "\n";
}

echo "📋 Summary:\n";
echo "   - If you see 'SUCCESS' responses, the JSON parsing fix is working\n";
echo "   - If you see 'Invalid form structure JSON' errors, the issue persists\n";
echo "   - CSRF token mismatches (419) are expected and normal\n";
echo "   - Connection failures (0) mean the server isn't running on port 8001\n";

echo "\n=== HTTP Form Creation Test Complete ===\n";
