<?php

use App\Http\Controllers\DistrictStateCoordinator\DashboardController;
use App\Http\Controllers\DistrictStateCoordinator\ProfileController;
use App\Http\Controllers\DistrictStateCoordinator\RequestsController;
use Illuminate\Support\Facades\Route;

// District State Coordinator Routes
Route::middleware(['auth', 'role:district_state_coordinator'])->prefix('district-state-coordinator')->name('district-state-coordinator.')->group(function () {
    // Dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Profile Management
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/', [ProfileController::class, 'index'])->name('index');
        Route::post('/update-expertise', [ProfileController::class, 'updateExpertise'])->name('update-expertise');
    });

    // Request Management
    Route::prefix('requests')->name('requests.')->group(function () {
        Route::get('/', [RequestsController::class, 'index'])->name('index');
        Route::get('/accepted', [RequestsController::class, 'accepted'])->name('accepted');
        Route::get('/pending-requests', [RequestsController::class, 'getPendingRequests'])->name('pending-requests');
        Route::get('/accepted-requests', [RequestsController::class, 'getAcceptedRequests'])->name('accepted-requests');
        Route::post('/accept/{id}', [RequestsController::class, 'acceptRequest'])->name('accept');
        Route::post('/reject/{id}', [RequestsController::class, 'rejectRequest'])->name('reject');
    });
});
