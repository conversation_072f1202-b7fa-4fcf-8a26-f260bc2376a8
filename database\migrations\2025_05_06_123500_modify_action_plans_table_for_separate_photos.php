<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('action_plans', function (Blueprint $table) {
            // First check if the photos column exists
            if (Schema::hasColumn('action_plans', 'photos')) {
                // Add new columns for individual photos
                $table->string('photo1')->nullable()->after('photos');
                $table->string('photo2')->nullable()->after('photo1');
                $table->string('photo3')->nullable()->after('photo2');
                $table->string('photo4')->nullable()->after('photo3');
                
                // Migrate existing data
                $this->migrateExistingPhotos();
            } else {
                // If photos column doesn't exist, just add the new columns
                $table->string('photo1')->nullable();
                $table->string('photo2')->nullable();
                $table->string('photo3')->nullable();
                $table->string('photo4')->nullable();
            }
        });
    }

    /**
     * Migrate existing photos from JSON array to individual columns
     */
    private function migrateExistingPhotos()
    {
        try {
            $actionPlans = DB::table('action_plans')
                ->whereNotNull('photos')
                ->get();
            
            foreach ($actionPlans as $plan) {
                try {
                    $photos = json_decode($plan->photos, true);
                    
                    if (is_array($photos)) {
                        $updates = [];
                        
                        if (isset($photos[0])) {
                            $updates['photo1'] = $photos[0];
                        }
                        
                        if (isset($photos[1])) {
                            $updates['photo2'] = $photos[1];
                        }
                        
                        if (isset($photos[2])) {
                            $updates['photo3'] = $photos[2];
                        }
                        
                        if (isset($photos[3])) {
                            $updates['photo4'] = $photos[3];
                        }
                        
                        if (!empty($updates)) {
                            DB::table('action_plans')
                                ->where('id', $plan->id)
                                ->update($updates);
                        }
                    }
                } catch (\Exception $e) {
                    Log::error('Error migrating photos for action plan ' . $plan->id . ': ' . $e->getMessage());
                }
            }
        } catch (\Exception $e) {
            Log::error('Error in migrateExistingPhotos: ' . $e->getMessage());
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('action_plans', function (Blueprint $table) {
            $table->dropColumn(['photo1', 'photo2', 'photo3', 'photo4']);
        });
    }
};
