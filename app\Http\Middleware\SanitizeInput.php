<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SanitizeInput
{
    /**
     * Fields that should not be HTML-encoded (typically JSON fields)
     */
    protected $jsonFields = [
        'form_structure',
        'form_data',
        'structure',
        'data'
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Sanitize all input data
        $input = $request->all();
        $sanitized = $this->sanitizeArray($input);
        $request->merge($sanitized);

        return $next($request);
    }
    
    /**
     * Recursively sanitize an array of data.
     */
    protected function sanitizeArray(array $data, string $parentKey = ''): array
    {
        $sanitized = [];

        foreach ($data as $key => $value) {
            $fullKey = $parentKey ? $parentKey . '.' . $key : $key;

            if (is_array($value)) {
                $sanitized[$key] = $this->sanitizeArray($value, $fullKey);
            } elseif (is_string($value)) {
                // Check if this field should be treated as JSON and not HTML-encoded
                if ($this->isJsonField($key, $fullKey)) {
                    $sanitized[$key] = $this->sanitizeJsonString($value);
                } else {
                    $sanitized[$key] = $this->sanitizeString($value);
                }
            } else {
                $sanitized[$key] = $value;
            }
        }

        return $sanitized;
    }
    
    /**
     * Check if a field should be treated as JSON (not HTML-encoded).
     */
    protected function isJsonField(string $key, string $fullKey): bool
    {
        // Check if the key matches any of our JSON fields
        foreach ($this->jsonFields as $jsonField) {
            if ($key === $jsonField || str_ends_with($fullKey, '.' . $jsonField)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Sanitize a JSON string value (minimal sanitization to preserve JSON structure).
     */
    protected function sanitizeJsonString(string $value): string
    {
        // Remove null bytes
        $value = str_replace("\0", '', $value);

        // Remove script tags and javascript (but don't HTML-encode)
        $value = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $value);
        $value = preg_replace('/javascript:/i', '', $value);
        $value = preg_replace('/vbscript:/i', '', $value);

        return trim($value);
    }

    /**
     * Sanitize a string value.
     */
    protected function sanitizeString(string $value): string
    {
        // Remove null bytes
        $value = str_replace("\0", '', $value);

        // Remove or escape potentially dangerous characters
        $value = htmlspecialchars($value, ENT_QUOTES | ENT_HTML5, 'UTF-8', false);

        // Remove script tags and javascript
        $value = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $value);
        $value = preg_replace('/javascript:/i', '', $value);
        $value = preg_replace('/vbscript:/i', '', $value);
        $value = preg_replace('/on\w+\s*=/i', '', $value);

        return trim($value);
    }
}
