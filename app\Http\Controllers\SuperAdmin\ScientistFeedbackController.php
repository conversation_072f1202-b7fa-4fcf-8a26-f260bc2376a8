<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\ScientistFeedback;
use App\Models\User;
use App\Models\Event;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ScientistFeedbackController extends Controller
{
    /**
     * Display the scientist feedback page.
     */
    public function index()
    {
        return view('super-admin.scientist-feedback.index');
    }

    /**
     * Get all feedback entries.
     */
    public function getAllFeedback()
    {
        try {
            $feedback = ScientistFeedback::with(['scientist:id,name,email', 'creator:id,name'])
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json($feedback);
        } catch (\Exception $e) {
            Log::error('Error getting feedback entries: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get feedback entries'], 500);
        }
    }

    /**
     * Get feedback details.
     */
    public function getFeedbackDetails($id)
    {
        try {
            $feedback = ScientistFeedback::with(['scientist:id,name,email', 'creator:id,name'])
                ->findOrFail($id);

            return response()->json($feedback);
        } catch (\Exception $e) {
            Log::error('Error getting feedback details: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get feedback details'], 500);
        }
    }

    /**
     * Create or update feedback.
     */
    public function saveFeedback(Request $request)
    {
        $request->validate([
            'scientist_id' => 'required|exists:users,id',
            'overall_rating' => 'required|integer|min:1|max:5',
            'preparedness_rating' => 'required|integer|min:1|max:5',
            'communication_rating' => 'required|integer|min:1|max:5',
            'engagement_rating' => 'required|integer|min:1|max:5',
            'adherence_rating' => 'required|integer|min:1|max:5',
            'remarks' => 'nullable|string',
        ]);

        try {
            // Ensure the scientist exists and has the correct role
            $scientist = User::where('id', $request->scientist_id)
                ->where('role', 'scientist')
                ->firstOrFail();

            if ($request->has('id')) {
                $feedback = ScientistFeedback::findOrFail($request->id);
                $feedback->update([
                    'scientist_id' => $request->scientist_id,
                    'overall_rating' => $request->overall_rating,
                    'preparedness_rating' => $request->preparedness_rating,
                    'communication_rating' => $request->communication_rating,
                    'engagement_rating' => $request->engagement_rating,
                    'adherence_rating' => $request->adherence_rating,
                    'remarks' => $request->remarks,
                    'created_by' => auth()->id(),
                ]);

                return response()->json(['message' => 'Feedback updated successfully', 'id' => $feedback->id]);
            } else {
                $feedback = ScientistFeedback::create([
                    'scientist_id' => $request->scientist_id,
                    'overall_rating' => $request->overall_rating,
                    'preparedness_rating' => $request->preparedness_rating,
                    'communication_rating' => $request->communication_rating,
                    'engagement_rating' => $request->engagement_rating,
                    'adherence_rating' => $request->adherence_rating,
                    'remarks' => $request->remarks,
                    'created_by' => auth()->id(),
                ]);

                return response()->json(['message' => 'Feedback created successfully', 'id' => $feedback->id]);
            }
        } catch (\Exception $e) {
            Log::error('Error saving feedback: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to save feedback'], 500);
        }
    }

    /**
     * Delete feedback.
     */
    public function deleteFeedback($id)
    {
        try {
            $feedback = ScientistFeedback::findOrFail($id);
            $feedback->delete();

            return response()->json(['message' => 'Feedback deleted successfully']);
        } catch (\Exception $e) {
            Log::error('Error deleting feedback: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete feedback'], 500);
        }
    }

    /**
     * Get all scientists for dropdown.
     */
    public function getScientists()
    {
        try {
            $scientists = User::where('role', 'scientist')
                ->select('id', 'name', 'email')
                ->orderBy('name')
                ->get();
            
            return response()->json($scientists);
        } catch (\Exception $e) {
            Log::error('Error getting scientists: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get scientists'], 500);
        }
    }


}
