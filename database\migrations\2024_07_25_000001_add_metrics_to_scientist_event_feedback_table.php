<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('scientist_event_feedback', function (Blueprint $table) {
            // Add new metrics fields
            $table->integer('event_success_rating')->nullable()->after('success_rating')->comment('1-5 stars');
            $table->text('challenges_faced')->nullable()->after('challenges');
            $table->text('improvement_suggestions')->nullable()->after('suggestions');
            $table->json('objectives_met_list')->nullable()->after('objectives_met');
            $table->text('self_assessed_learning')->nullable()->after('learning_outcome');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('scientist_event_feedback', function (Blueprint $table) {
            // Remove the added columns
            $table->dropColumn([
                'event_success_rating',
                'challenges_faced',
                'improvement_suggestions',
                'objectives_met_list',
                'self_assessed_learning',
            ]);
        });
    }
};
