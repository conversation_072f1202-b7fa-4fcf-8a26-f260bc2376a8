<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Action Plan Feedback Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .event-details {
            margin-bottom: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .analytics-section {
            margin-bottom: 30px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        .stat-box {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .page-break {
            page-break-after: always;
        }
        .footer {
            text-align: center;
            font-size: 10px;
            color: #777;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Action Plan Feedback Report</h1>
        <p>Generated on: {{ date('Y-m-d H:i:s') }}</p>
    </div>

    @if(isset($action_plan))
    <div class="event-details">
        <h2>Action Plan Details</h2>
        <p><strong>Title:</strong> {{ $action_plan->title }}</p>
        <p><strong>Date:</strong> {{ date('Y-m-d', strtotime($action_plan->planned_date)) }}</p>
        <p><strong>District:</strong> {{ $action_plan->district->district ?? 'N/A' }}</p>
        <p><strong>State:</strong> {{ $action_plan->district->state ?? 'N/A' }}</p>
    </div>
    @endif

    @if($include_analytics && isset($analytics))
    <div class="analytics-section">
        <h2>Feedback Analytics</h2>

        <div class="stats-grid">
            <div class="stat-box">
                <h3>Total Feedback</h3>
                <p>{{ $analytics['total_feedback'] }}</p>
            </div>
            <div class="stat-box">
                <h3>Avg. Event Success Rating</h3>
                <p>{{ $analytics['avg_event_success_rating'] }}/10</p>
            </div>
        </div>

        <h3>Participant Demographics</h3>
        <table>
            <tr>
                <th>Category</th>
                <th>Count</th>
            </tr>
            <tr>
                <td>Male</td>
                <td>{{ $analytics['total_participants']['male'] }}</td>
            </tr>
            <tr>
                <td>Female</td>
                <td>{{ $analytics['total_participants']['female'] }}</td>
            </tr>
            <tr>
                <td>Transgender</td>
                <td>{{ $analytics['total_participants']['transgender'] }}</td>
            </tr>
            <tr>
                <td>ST</td>
                <td>{{ $analytics['total_participants']['st'] }}</td>
            </tr>
            <tr>
                <td>SC</td>
                <td>{{ $analytics['total_participants']['sc'] }}</td>
            </tr>
            <tr>
                <td>General</td>
                <td>{{ $analytics['total_participants']['general'] }}</td>
            </tr>
            <tr>
                <td>OBC</td>
                <td>{{ $analytics['total_participants']['obc'] }}</td>
            </tr>
        </table>

        @if(!empty($analytics['top_objectives']))
        <h3>Top Objectives Met</h3>
        <table>
            <tr>
                <th>Objective</th>
                <th>Count</th>
            </tr>
            @foreach($analytics['top_objectives'] as $objective => $count)
            <tr>
                <td>{{ $objective }}</td>
                <td>{{ $count }}</td>
            </tr>
            @endforeach
        </table>
        @endif

        @if(!empty($analytics['challenges']))
        <h3>Recent Challenges</h3>
        <ul>
            @foreach($analytics['challenges'] as $challenge)
            <li>{{ $challenge }}</li>
            @endforeach
        </ul>
        @endif

        @if(!empty($analytics['suggestions']))
        <h3>Recent Suggestions</h3>
        <ul>
            @foreach($analytics['suggestions'] as $suggestion)
            <li>{{ $suggestion }}</li>
            @endforeach
        </ul>
        @endif
    </div>

    <div class="page-break"></div>
    @endif

    <h2>Feedback Details</h2>

    @if($feedback->isEmpty())
    <p>No feedback data available.</p>
    @else
    <table>
        <thead>
            <tr>
                <th>Action Plan</th>
                <th>Event Success Rating</th>
                <th>Challenges Faced</th>
                <th>Suggestions</th>
                <th>Objectives Met</th>
                <th>Learning Outcome</th>
                <th>Date</th>
            </tr>
        </thead>
        <tbody>
            @foreach($feedback as $item)
            <tr>
                <td>{{ $item->actionPlan->title ?? 'N/A' }}</td>
                <td>{{ $item->event_success_rating }}/10</td>
                <td>{{ $item->challenges_faced ?? 'N/A' }}</td>
                <td>{{ $item->suggestions_for_improvement ?? 'N/A' }}</td>
                <td>{{ is_array($item->objectives_met) ? implode(', ', $item->objectives_met) : 'N/A' }}</td>
                <td>{{ $item->self_assessed_learning_outcome ?? 'N/A' }}</td>
                <td>{{ date('Y-m-d', strtotime($item->created_at)) }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @endif

    <div class="footer">
        <p>Generated by {{ config('app.name') }} on {{ date('Y-m-d H:i:s') }}</p>
    </div>
</body>
</html>
