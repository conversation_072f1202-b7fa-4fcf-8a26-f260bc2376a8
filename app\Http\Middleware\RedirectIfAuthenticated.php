<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$guards): Response
    {
        $guards = empty($guards) ? [null] : $guards;

        foreach ($guards as $guard) {
            // Skip redirection for login route and public routes
            if ($request->is('login') || $request->is('direct-login.php') ||
                $request->is('/') || $request->is('about-us') ||
                $request->is('who-is-who') || $request->is('contact-us')) {
                return $next($request);
            }

            if (Auth::guard($guard)->check()) {
                // Redirect based on user role
                if (Auth::user()->role === 'super_admin') {
                    return redirect()->route('super-admin.dashboard');
                } else if (Auth::user()->role === 'admin') {
                    return redirect()->route('admin.dashboard');
                } else if (Auth::user()->role === 'scientist') {
                    return redirect()->route('scientist.dashboard');
                } else if (Auth::user()->role === 'zonal_coordinator') {
                    return redirect()->route('zonal-coordinator.dashboard');
                } else if (Auth::user()->role === 'district_state_coordinator') {
                    return redirect()->route('district-state-coordinator.dashboard');
                } else {
                    return redirect()->route('user.dashboard');
                }
            }
        }

        return $next($request);
    }
}
