@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Assign Scientists to Districts') }}</h5>
                    <div>
                        <a href="{{ route('zonal-coordinator.scientists.index') }}" class="btn btn-sm btn-secondary">Back to Scientists</a>
                        <a href="{{ route('zonal-coordinator.dashboard') }}" class="btn btn-sm btn-secondary">Dashboard</a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>Assign scientists to districts in your zone.</li>
                            <li>Each district can have only one scientist assigned.</li>
                            <li>Scientists can be assigned to multiple districts.</li>
                        </ul>
                    </div>

                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6">
                            <h6>Your Districts</h6>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>District</th>
                                            <th>State</th>
                                            <th>Status</th>
                                            <th>Current Scientist</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($districts as $district)
                                            <tr>
                                                <td>{{ $district->district }}</td>
                                                <td>{{ $district->state }}</td>
                                                <td>
                                                    <span class="badge {{ $district->status === 'Pre-Cocoon' ? 'bg-success' : 'bg-info' }}">
                                                        {{ $district->status }}
                                                    </span>
                                                </td>
                                                <td>
                                                    @if($district->scientist)
                                                        <span class="text-success">{{ $district->scientist }}</span>
                                                    @else
                                                        <span class="text-muted">No scientist assigned</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary assign-btn" 
                                                            data-district-id="{{ $district->id }}"
                                                            data-district-name="{{ $district->state }} - {{ $district->district }}"
                                                            data-current-scientist="{{ $district->scientist }}">
                                                        {{ $district->scientist ? 'Change' : 'Assign' }}
                                                    </button>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h6>Available Scientists</h6>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Phone</th>
                                            <th>Designation</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($scientists as $scientist)
                                            <tr>
                                                <td>{{ $scientist->name }}</td>
                                                <td>{{ $scientist->email }}</td>
                                                <td>{{ $scientist->phone_number ?? 'N/A' }}</td>
                                                <td>{{ $scientist->designation ?? 'N/A' }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Assignment Modal -->
<div class="modal fade" id="assignModal" tabindex="-1" aria-labelledby="assignModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignModalLabel">Assign Scientist to District</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="assignForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="districtName" class="form-label">District</label>
                        <input type="text" class="form-control" id="districtName" readonly>
                        <input type="hidden" id="districtId" name="district_id">
                    </div>
                    <div class="mb-3">
                        <label for="scientistSelect" class="form-label">Select Scientist</label>
                        <select class="form-select" id="scientistSelect" name="scientist_email" required>
                            <option value="">Choose a scientist...</option>
                            @foreach($scientists as $scientist)
                                <option value="{{ $scientist->email }}">{{ $scientist->name }} ({{ $scientist->email }})</option>
                            @endforeach
                        </select>
                    </div>
                    <div id="currentScientistInfo" class="alert alert-warning" style="display: none;">
                        <strong>Current Scientist:</strong> <span id="currentScientistEmail"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Assign Scientist</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const assignModal = new bootstrap.Modal(document.getElementById('assignModal'));
        const assignForm = document.getElementById('assignForm');
        const districtNameInput = document.getElementById('districtName');
        const districtIdInput = document.getElementById('districtId');
        const scientistSelect = document.getElementById('scientistSelect');
        const currentScientistInfo = document.getElementById('currentScientistInfo');
        const currentScientistEmail = document.getElementById('currentScientistEmail');

        // Handle assign button clicks
        document.querySelectorAll('.assign-btn').forEach(button => {
            button.addEventListener('click', function() {
                const districtId = this.dataset.districtId;
                const districtName = this.dataset.districtName;
                const currentScientist = this.dataset.currentScientist;

                districtNameInput.value = districtName;
                districtIdInput.value = districtId;

                if (currentScientist) {
                    currentScientistInfo.style.display = 'block';
                    currentScientistEmail.textContent = currentScientist;
                    scientistSelect.value = currentScientist;
                } else {
                    currentScientistInfo.style.display = 'none';
                    scientistSelect.value = '';
                }

                assignModal.show();
            });
        });

        // Handle form submission
        assignForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch('{{ route("zonal-coordinator.scientists.assign.save") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert('Error: ' + data.error);
                } else {
                    alert('Success: ' + data.message);
                    assignModal.hide();
                    location.reload(); // Refresh the page to show updated assignments
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while assigning the scientist.');
            });
        });
    });
</script>
@endpush
