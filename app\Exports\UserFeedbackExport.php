<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class UserFeedbackExport implements FromCollection, WithHeadings
{
    public function collection()
    {
        return collect([
            [
                'Name',
                'Contact',
                'Benefit Rating (1-10)',
                'Would Recommend (Yes/No)',
                'Most Helpful Topic',
                'Speaker Rating (1-5)',
                'Improvement Suggestions'
            ]
        ]);
    }

    public function headings(): array
    {
        return [
            'Name',
            'Contact',
            'Benefit Rating (1-10)',
            'Would Recommend (Yes/No)',
            'Most Helpful Topic',
            'Speaker Rating (1-5)',
            'Improvement Suggestions'
        ];
    }
}
