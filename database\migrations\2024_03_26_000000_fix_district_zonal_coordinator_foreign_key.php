<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('district_zonal_coordinator', function (Blueprint $table) {
            // First, drop any existing foreign key constraints
            $foreignKeys = $this->getForeignKeys('district_zonal_coordinator');
            foreach ($foreignKeys as $foreignKey) {
                $table->dropForeign($foreignKey);
            }

            // Drop the existing unique constraint
            $table->dropUnique('district_zc_unique');

            // Drop the existing district_id column
            $table->dropColumn('district_id');

            // Add the district_id column back with proper foreign key
            $table->foreignId('district_id')
                ->after('zonal_coordinator_id')
                ->constrained('state_data')
                ->onDelete('cascade');

            // Add the unique constraint back
            $table->unique(['zonal_coordinator_id', 'district_id'], 'district_zc_unique');
        });
    }

    public function down()
    {
        Schema::table('district_zonal_coordinator', function (Blueprint $table) {
            // Drop the unique constraint
            $table->dropUnique('district_zc_unique');

            // Drop the foreign key constraint
            $table->dropForeign(['district_id']);

            // Drop the district_id column
            $table->dropColumn('district_id');

            // Add the district_id column back without foreign key
            $table->unsignedBigInteger('district_id')->after('zonal_coordinator_id');

            // Add the unique constraint back
            $table->unique(['zonal_coordinator_id', 'district_id'], 'district_zc_unique');
        });
    }

    private function getForeignKeys($table)
    {
        $conn = Schema::getConnection()->getDoctrineSchemaManager();
        $foreignKeys = [];

        try {
            $foreignKeys = $conn->listTableForeignKeys($table);
        } catch (\Exception $e) {
            // Table might not exist yet
        }

        return array_map(function($fk) {
            return $fk->getName();
        }, $foreignKeys);
    }
};
