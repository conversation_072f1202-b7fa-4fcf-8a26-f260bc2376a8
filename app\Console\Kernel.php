<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Send daily feedback summaries at 8:00 AM
        $schedule->command('feedback:send-summaries --type=daily')
                 ->dailyAt('08:00')
                 ->appendOutputTo(storage_path('logs/feedback-daily-summaries.log'));

        // Send weekly feedback summaries on Monday at 9:00 AM
        $schedule->command('feedback:send-summaries --type=weekly')
                 ->weeklyOn(1, '09:00')
                 ->appendOutputTo(storage_path('logs/feedback-weekly-summaries.log'));

        // Check for low ratings every 6 hours
        $schedule->command('feedback:send-low-rating-alerts --days=1')
                 ->everyFourHours()
                 ->appendOutputTo(storage_path('logs/feedback-low-rating-alerts.log'));

        // Send new feedback notifications every hour
        $schedule->command('feedback:send-new-notifications --minutes=60')
                 ->hourly()
                 ->appendOutputTo(storage_path('logs/feedback-new-notifications.log'));


    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
