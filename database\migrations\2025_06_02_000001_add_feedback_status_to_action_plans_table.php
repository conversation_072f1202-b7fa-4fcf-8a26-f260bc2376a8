<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('action_plans', function (Blueprint $table) {
            if (!Schema::hasColumn('action_plans', 'scientist_feedback_status')) {
                $table->string('scientist_feedback_status')->nullable();
            }
            if (!Schema::hasColumn('action_plans', 'zc_feedback_status')) {
                $table->string('zc_feedback_status')->nullable();
            }
            if (!Schema::hasColumn('action_plans', 'dsc_feedback_status')) {
                $table->string('dsc_feedback_status')->nullable();
            }
            if (!Schema::hasColumn('action_plans', 'super_admin_feedback_status')) {
                $table->string('super_admin_feedback_status')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('action_plans', function (Blueprint $table) {
            $table->dropColumn([
                'scientist_feedback_status',
                'zc_feedback_status',
                'dsc_feedback_status',
                'super_admin_feedback_status'
            ]);
        });
    }
};
