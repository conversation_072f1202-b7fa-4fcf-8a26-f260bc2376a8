<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('action_plans', function (Blueprint $table) {
            // Add Scientist Feedback with Metrics fields
            $table->integer('event_success_rating')->nullable()->after('status')->comment('1-5 stars');
            $table->text('challenges_faced')->nullable()->after('event_success_rating');
            $table->text('suggestions_for_improvement')->nullable()->after('challenges_faced');
            $table->json('objectives_met')->nullable()->after('suggestions_for_improvement');
            $table->text('self_assessed_learning_outcome')->nullable()->after('objectives_met');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('action_plans', function (Blueprint $table) {
            // Remove the added columns
            $table->dropColumn([
                'event_success_rating',
                'challenges_faced',
                'suggestions_for_improvement',
                'objectives_met',
                'self_assessed_learning_outcome',
            ]);
        });
    }
};
