<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        try {
            // Get all tables that start with 'form_'
            $tables = DB::select("SHOW TABLES LIKE 'form_%'");
            
            Log::info("Found " . count($tables) . " form tables to check");
            
            foreach ($tables as $table) {
                $tableName = array_values((array)$table)[0];
                
                Log::info("Checking table: {$tableName}");
                
                // Get all columns for this table
                $columns = DB::select("SHOW COLUMNS FROM `{$tableName}`");
                
                foreach ($columns as $column) {
                    $columnName = $column->Field;
                    $columnType = $column->Type;
                    
                    // Check if this is a phone number column (by name) and it's an integer type
                    if ((stripos($columnName, 'phone') !== false || stripos($columnName, 'mobile') !== false) && 
                        stripos($columnType, 'int') !== false) {
                        
                        Log::info("Found integer phone column: {$tableName}.{$columnName} ({$columnType})");
                        
                        // Change the column type to VARCHAR
                        try {
                            DB::statement("ALTER TABLE `{$tableName}` MODIFY `{$columnName}` VARCHAR(20)");
                            Log::info("Successfully changed {$tableName}.{$columnName} from {$columnType} to VARCHAR(20)");
                        } catch (\Exception $e) {
                            Log::error("Failed to change column type for {$tableName}.{$columnName}: " . $e->getMessage());
                            // Continue with other columns instead of failing completely
                        }
                    }
                }
            }
            
            Log::info("Completed phone column type fixes for all form tables");
            
        } catch (\Exception $e) {
            Log::error("Error in fix_all_form_tables_phone_columns migration: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // We don't want to revert back to integer types as it would cause data loss
        Log::info("Not reverting phone column changes to prevent data loss");
    }
};
