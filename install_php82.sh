#!/bin/bash

echo "Installing PHP 8.2..."

# Update package list
sudo apt-get update

# Install required packages
sudo apt-get -y install lsb-release ca-certificates curl

# Add Sury PHP repository
sudo curl -sSLo /tmp/debsuryorg-archive-keyring.deb https://packages.sury.org/debsuryorg-archive-keyring.deb
sudo dpkg -i /tmp/debsuryorg-archive-keyring.deb
sudo sh -c 'echo "deb [signed-by=/usr/share/keyrings/debsuryorg-archive-keyring.gpg] https://packages.sury.org/php/ $(lsb_release -sc) main" > /etc/apt/sources.list.d/php.list'

# Update package list again
sudo apt-get update

# Install PHP 8.2 and required extensions
sudo apt-get install -y php8.2 php8.2-cli php8.2-common php8.2-mysql php8.2-xml php8.2-curl php8.2-gd php8.2-mbstring php8.2-zip php8.2-bcmath php8.2-intl php8.2-sqlite3 php8.2-opcache

# Set PHP 8.2 as default
sudo update-alternatives --install /usr/bin/php php /usr/bin/php8.2 82
sudo update-alternatives --set php /usr/bin/php8.2

echo "PHP 8.2 installation completed!"
echo "Current PHP version:"
php --version
