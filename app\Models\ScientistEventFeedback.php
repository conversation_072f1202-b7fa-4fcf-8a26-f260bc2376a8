<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ScientistEventFeedback extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'action_plan_id',
        'scientist_id',
        'success_rating',
        'event_success_rating',
        'challenges',
        'challenges_faced',
        'suggestions',
        'improvement_suggestions',
        'objectives_met',
        'objectives_met_list',
        'learning_outcome',
        'self_assessed_learning',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'objectives_met' => 'array',
        'objectives_met_list' => 'array',
    ];

    /**
     * Get the action plan associated with this feedback.
     */
    public function actionPlan()
    {
        return $this->belongsTo(ActionPlan::class);
    }

    /**
     * Get the scientist who provided this feedback.
     */
    public function scientist()
    {
        return $this->belongsTo(User::class, 'scientist_id');
    }
}
