<?php

namespace App\Http\Controllers;

use App\Models\ActionPlan;
use App\Models\ActionPlanFeedback;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ActionPlanFeedbackController extends Controller
{
    public function showForm($actionPlanId)
    {
        $actionPlan = ActionPlan::findOrFail($actionPlanId);
        return view('feedback.form', compact('actionPlan'));
    }

    public function submit(Request $request, $actionPlanId)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'contact' => 'required|string|max:20',
            'benefit_rating' => 'required|integer|min:1|max:5',
            'speaker_rating' => 'required|integer|min:1|max:5',
            'would_recommend' => 'required|boolean',
            'most_helpful_topic' => 'required|string|max:255',
            'suggestions' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Check for duplicate feedback
        $existingFeedback = ActionPlanFeedback::where([
            ['action_plan_id', $actionPlanId],
            ['contact', $request->contact]
        ])->first();

        if ($existingFeedback) {
            return redirect()->back()
                ->with('error', 'You have already submitted feedback for this action plan.');
        }

        ActionPlanFeedback::create([
            'action_plan_id' => $actionPlanId,
            'name' => $request->name,
            'contact' => $request->contact,
            'benefit_rating' => $request->benefit_rating,
            'speaker_rating' => $request->speaker_rating,
            'would_recommend' => $request->would_recommend,
            'most_helpful_topic' => $request->most_helpful_topic,
            'suggestions' => $request->suggestions
        ]);

        return redirect()->route('feedback.success');
    }

    public function viewFeedback($actionPlanId)
    {
        $actionPlan = ActionPlan::findOrFail($actionPlanId);
        $feedback = ActionPlanFeedback::where('action_plan_id', $actionPlanId)
            ->orderBy('created_at', 'desc')
            ->get();

        return view('feedback.view', compact('actionPlan', 'feedback'));
    }

    public function exportFeedback($actionPlanId)
    {
        $actionPlan = ActionPlan::findOrFail($actionPlanId);
        $filename = 'feedback_' . $actionPlan->title . '_' . date('Y-m-d') . '.xlsx';

        return Excel::download(new ActionPlanFeedbackExport($actionPlanId), $filename);
    }

    public function downloadTemplate()
    {
        return Excel::download(new ActionPlanFeedbackExport(null), 'feedback_template.xlsx');
    }

    public function importFeedback(Request $request, $actionPlanId)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls'
        ]);

        Excel::import(new ActionPlanFeedbackImport($actionPlanId), $request->file('file'));

        return redirect()->back()->with('success', 'Feedback imported successfully');
    }
}
