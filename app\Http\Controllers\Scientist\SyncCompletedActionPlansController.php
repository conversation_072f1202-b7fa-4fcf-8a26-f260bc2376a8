<?php

namespace App\Http\Controllers\Scientist;

use App\Http\Controllers\Controller;
use App\Models\ActionPlan;
use App\Models\Event;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class SyncCompletedActionPlansController extends Controller
{
    /**
     * Sync completed action plans to the events table.
     */
    public function syncCompletedActionPlans()
    {
        try {
            // Check if user is authenticated
            if (!Auth::check()) {
                Log::error('User not authenticated when trying to sync completed action plans');
                return response()->json(['error' => 'User not authenticated'], 401);
            }
            
            // Get the scientist ID
            $scientistId = Auth::id();
            
            // Get all completed action plans
            $completedPlans = ActionPlan::where('status', 'completed')->get();
            
            Log::info('Syncing completed action plans to events table', [
                'scientist_id' => $scientistId,
                'completed_plans_count' => $completedPlans->count()
            ]);
            
            $syncedCount = 0;
            $createdCount = 0;
            $updatedCount = 0;
            
            foreach ($completedPlans as $plan) {
                // Check if an event with the same ID already exists
                $event = Event::find($plan->id);
                
                if (!$event) {
                    // Create a new event
                    $event = new Event();
                    $event->id = $plan->id;
                    $createdCount++;
                } else {
                    $updatedCount++;
                }
                
                // Update the event with the action plan data
                $event->title = $plan->title;
                $event->description = "Synced from action plan ID: {$plan->id}";
                $event->start_date = $plan->planned_date;
                $event->end_date = $plan->planned_date;
                $event->location = $plan->location;
                $event->district_id = $plan->district_id;
                $event->scientist_id = $plan->scientist_id;
                $event->expected_participants = $plan->expected_participants;
                
                // Calculate actual participants
                $actualParticipants = ($plan->male_participants ?? 0) + 
                                     ($plan->female_participants ?? 0) + 
                                     ($plan->transgender_participants ?? 0);
                
                $event->actual_participants = $actualParticipants;
                
                // Create participant demographics JSON
                $demographics = [
                    'male' => $plan->male_participants ?? 0,
                    'female' => $plan->female_participants ?? 0,
                    'transgender' => $plan->transgender_participants ?? 0,
                    'st' => $plan->st_participants ?? 0,
                    'sc' => $plan->sc_participants ?? 0,
                    'general' => $plan->general_participants ?? 0,
                    'obc' => $plan->obc_participants ?? 0
                ];
                
                $event->participant_demographics = json_encode($demographics);
                $event->topic = $plan->type;
                $event->status = $plan->status;
                $event->created_at = $plan->created_at;
                $event->updated_at = $plan->updated_at;
                
                $event->save();
                $syncedCount++;
                
                Log::info('Synced action plan to event', [
                    'action_plan_id' => $plan->id,
                    'event_id' => $event->id,
                    'title' => $event->title,
                    'status' => $event->status
                ]);
            }
            
            return response()->json([
                'success' => true,
                'message' => "Successfully synced {$syncedCount} action plans to events table. Created: {$createdCount}, Updated: {$updatedCount}",
                'synced_count' => $syncedCount,
                'created_count' => $createdCount,
                'updated_count' => $updatedCount
            ]);
        } catch (\Exception $e) {
            Log::error('Error syncing completed action plans', [
                'scientist_id' => Auth::id() ?? 'not authenticated',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'error' => 'Failed to sync completed action plans: ' . $e->getMessage()
            ], 500);
        }
    }
}
