<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use App\Models\User;
use App\Models\ActionPlan;
use App\Models\StateData;
use App\Models\UserFeedback;
use App\Models\FormBuilder;
use Illuminate\Support\Facades\DB;

class PublicPageController extends Controller
{
    /**
     * Show the home page.
     */
    public function home()
    {
        // Get statistics from the database
        $stats = [
            'total_users' => User::count(),
            'scientists' => User::where('role', 'scientist')->count(),
            'districts' => StateData::distinct('district')->count(),
            'states' => StateData::distinct('state')->count(),
            'action_plans' => ActionPlan::count(),
            'completed_action_plans' => ActionPlan::where('status', 'completed')->count(),
            'total_participants' => ActionPlan::sum('male_participants') + ActionPlan::sum('female_participants') + ActionPlan::sum('transgender_participants'),
            'feedback_count' => UserFeedback::count(),
            'pre_cocoon_districts' => StateData::where('status', 'Pre-Cocoon')->count(),
            'post_cocoon_districts' => StateData::where('status', 'Post-Cocoon')->count(),
            'forms_count' => FormBuilder::count(),
            'zonal_coordinators' => User::where('role', 'zonal_coordinator')->count(),
            'district_coordinators' => User::where('role', 'district_state_coordinator')->count(),
        ];

        return view('public.homepage', compact('stats'));
    }

    /**
     * Show the about us page.
     */
    public function aboutUs()
    {
        return view('public.about-us');
    }

    /**
     * Show the who is who page.
     */
    public function whoIsWho()
    {
        return view('public.who-is-who-page');
    }

    /**
     * Show the contact us page.
     */
    public function contactUs()
    {
        return view('public.contact-us');
    }

    /**
     * Handle contact form submission.
     */
    public function submitContact(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'message' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput();
        }

        // Here you can add email sending logic if needed
        // For now, we'll just show a success message

        return back()->with('success', 'Thank you for your message! We will get back to you soon.');
    }
}
