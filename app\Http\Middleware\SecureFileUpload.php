<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SecureFileUpload
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if request has files
        $allFiles = $request->allFiles();
        if (!empty($allFiles)) {
            foreach ($allFiles as $key => $files) {
                // Handle both single files and arrays of files
                $filesToCheck = is_array($files) ? $files : [$files];
                
                foreach ($filesToCheck as $file) {
                    if ($file && $file->isValid()) {
                        // Check file size (max 10MB)
                        if ($file->getSize() > 10485760) {
                            return response()->json([
                                'error' => 'File size exceeds maximum allowed size of 10MB'
                            ], 413);
                        }
                        
                        // Check file extension
                        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'csv', 'txt'];
                        $extension = strtolower($file->getClientOriginalExtension());
                        
                        if (!in_array($extension, $allowedExtensions)) {
                            return response()->json([
                                'error' => 'File type not allowed. Allowed types: ' . implode(', ', $allowedExtensions)
                            ], 422);
                        }
                        
                        // Check MIME type
                        $allowedMimeTypes = [
                            'image/jpeg', 'image/png', 'image/gif',
                            'application/pdf',
                            'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                            'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                            'text/csv', 'text/plain'
                        ];
                        
                        if (!in_array($file->getMimeType(), $allowedMimeTypes)) {
                            return response()->json([
                                'error' => 'Invalid file type detected'
                            ], 422);
                        }
                        
                        // Check for executable files by content
                        try {
                            $fileContent = file_get_contents($file->getRealPath());
                            $dangerousPatterns = [
                                '<?php', '<?=', '<script', 'javascript:', 'vbscript:',
                                'onload=', 'onerror=', 'onclick=', 'onmouseover='
                            ];

                            foreach ($dangerousPatterns as $pattern) {
                                if (stripos($fileContent, $pattern) !== false) {
                                    return response()->json([
                                        'error' => 'File contains potentially dangerous content'
                                    ], 422);
                                }
                            }
                        } catch (\Exception $e) {
                            // If we can't read the file content, allow it to pass
                            // The file upload will handle any actual file errors
                            \Illuminate\Support\Facades\Log::warning('Could not scan file content for security', [
                                'file' => $file->getClientOriginalName(),
                                'error' => $e->getMessage()
                            ]);
                        }
                    }
                }
            }
        }
        
        return $next($request);
    }
}
