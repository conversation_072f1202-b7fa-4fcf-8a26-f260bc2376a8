<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;

class AuthRateLimit
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $key = $this->resolveRequestSignature($request);
        
        // Allow 5 login attempts per minute per IP
        if (RateLimiter::tooManyAttempts($key, 5)) {
            $seconds = RateLimiter::availableIn($key);
            
            return response()->json([
                'error' => 'Too many login attempts. Please try again in ' . ceil($seconds / 60) . ' minutes.'
            ], 429);
        }
        
        $response = $next($request);
        
        // If login failed, increment the rate limiter
        if ($response->getStatusCode() === 302 && $request->session()->has('errors')) {
            RateLimiter::hit($key, 60); // 60 seconds decay
        }
        
        // If login succeeded, clear the rate limiter
        if ($response->getStatusCode() === 302 && !$request->session()->has('errors')) {
            RateLimiter::clear($key);
        }
        
        return $response;
    }
    
    /**
     * Resolve the request signature for rate limiting.
     */
    protected function resolveRequestSignature(Request $request): string
    {
        return 'login_attempts:' . $request->ip();
    }
}
