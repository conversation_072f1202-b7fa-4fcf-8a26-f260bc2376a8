<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('form_builders', function (Blueprint $table) {
            // Add unique constraint to form_name column
            $table->unique('form_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('form_builders', function (Blueprint $table) {
            // Drop the unique constraint
            $table->dropUnique(['form_name']);
        });
    }
};
