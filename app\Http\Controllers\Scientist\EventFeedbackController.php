<?php

namespace App\Http\Controllers\Scientist;

use App\Http\Controllers\Controller;
use App\Models\ActionPlan;
use App\Models\ScientistEventFeedback;
use App\Models\StateData;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class EventFeedbackController extends Controller
{
    /**
     * Get the scientist's district.
     * If no district is assigned, create a default one.
     */
    private function getScientistDistrict()
    {
        $scientistEmail = Auth::user()->email;
        $district = StateData::where('scientist', $scientistEmail)->first();

        // If no district is assigned, create a default one
        if (!$district) {
            Log::warning('No district found for scientist, creating default district', [
                'scientist_email' => $scientistEmail
            ]);

            try {
                // Create a default district for the scientist
                $district = StateData::create([
                    'state' => 'Default State',
                    'district' => 'Default District',
                    'state_name' => 'Default State',
                    'district_name' => 'Default District',
                    'scientist' => $scientistEmail,
                    'status' => 'pre-cocoon', // Default status
                ]);

                Log::info('Default district created for scientist', [
                    'district_id' => $district->id,
                    'scientist_email' => $scientistEmail
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to create default district for scientist', [
                    'scientist_email' => $scientistEmail,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $district;
    }
    /**
     * Helper function to create a test action plan and mark it as completed for testing.
     * This is a temporary function and should be removed in production.
     */
    public function createTestCompletedEvent()
    {
        try {
            // Get the scientist's district
            $district = $this->getScientistDistrict();

            if (!$district) {
                return response()->json(['error' => 'No district found for scientist'], 404);
            }

            // Create a new action plan
            $actionPlan = ActionPlan::create([
                'scientist_id' => Auth::id(),
                'district_id' => $district->id,
                'type' => 'training',
                'title' => 'Test Training Event ' . date('Y-m-d H:i:s'),
                'location' => 'Test Location',
                'planned_date' => now()->subDays(1), // Yesterday
                'expected_participants' => 30,
                'required_coordinators' => null,
                'status' => 'completed',
                'male_participants' => 10,
                'female_participants' => 15,
                'transgender_participants' => 2,
                'st_participants' => 5,
                'sc_participants' => 8,
                'general_participants' => 10,
                'obc_participants' => 4,
                'photos' => ['action-plan-photos/test1.jpg', 'action-plan-photos/test2.jpg'],
                'photo1' => 'action-plan-photos/test1.jpg',
                'photo2' => 'action-plan-photos/test2.jpg',
                'photo3' => 'action-plan-photos/test3.jpg',
                'photo4' => 'action-plan-photos/test4.jpg',
                'gis_location' => '12.9716,77.5946',
            ]);

            return response()->json([
                'message' => 'Test completed event created successfully',
                'action_plan' => $actionPlan
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating test completed event: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Failed to create test completed event: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Helper function to mark an action plan as completed for testing.
     * This is a temporary function and should be removed in production.
     */
    public function markActionPlanAsCompleted($id)
    {
        try {
            $actionPlan = ActionPlan::where('id', $id)
                ->where('scientist_id', Auth::id())
                ->first();

            if (!$actionPlan) {
                return response()->json(['error' => 'Action plan not found'], 404);
            }

            // Update the action plan status to completed
            $actionPlan->update([
                'status' => 'completed',
                // Add some dummy participant data
                'male_participants' => 10,
                'female_participants' => 15,
                'transgender_participants' => 2,
                'st_participants' => 5,
                'sc_participants' => 8,
                'general_participants' => 10,
                'obc_participants' => 4,
            ]);

            return response()->json([
                'message' => 'Action plan marked as completed successfully',
                'action_plan' => $actionPlan
            ]);
        } catch (\Exception $e) {
            Log::error('Error marking action plan as completed: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to mark action plan as completed'], 500);
        }
    }
    /**
     * Display the event feedback page.
     */
    public function index()
    {
        return view('scientist.feedback.index');
    }

    /**
     * Get completed action plans that need feedback.
     */
    public function getCompletedEvents()
    {
        try {
            // Check if user is authenticated
            if (!Auth::check()) {
                Log::error('User not authenticated when trying to get completed events');
                return response()->json(['error' => 'User not authenticated'], 401);
            }

            // Log the scientist ID for debugging
            $scientistId = Auth::id();
            $scientistEmail = Auth::user()->email;

            Log::info('Getting completed action plans for scientist', [
                'scientist_id' => $scientistId,
                'scientist_email' => $scientistEmail
            ]);

            // Get all completed action plans regardless of scientist_id for debugging
            $allCompletedPlans = ActionPlan::where('status', 'completed')->get();

            Log::info('All completed action plans in the system', [
                'count' => $allCompletedPlans->count(),
                'plans' => $allCompletedPlans->map(function($plan) {
                    return [
                        'id' => $plan->id,
                        'scientist_id' => $plan->scientist_id,
                        'title' => $plan->title,
                        'status' => $plan->status
                    ];
                })->toArray()
            ]);

            // IMPORTANT: For debugging, get ALL completed action plans regardless of scientist_id
            $completedPlans = ActionPlan::where('status', 'completed')
                ->orderBy('planned_date', 'desc')
                ->get();

            Log::info('IMPORTANT: Returning ALL completed action plans for debugging');

            Log::info('Completed action plans found', [
                'count' => $completedPlans->count(),
                'plans' => $completedPlans->map(function($plan) {
                    return [
                        'id' => $plan->id,
                        'scientist_id' => $plan->scientist_id,
                        'title' => $plan->title,
                        'status' => $plan->status,
                        'planned_date' => $plan->planned_date
                    ];
                })->toArray()
            ]);

            // If no completed plans, return empty array
            if ($completedPlans->isEmpty()) {
                Log::info('No completed action plans found');
                return response()->json([]);
            }

            // Filter out plans that already have feedback
            $needFeedback = $completedPlans->filter(function($plan) {
                $hasFeedback = ScientistEventFeedback::where('action_plan_id', $plan->id)->exists();
                Log::info('Checking if plan has feedback', [
                    'plan_id' => $plan->id,
                    'title' => $plan->title,
                    'has_feedback' => $hasFeedback
                ]);
                return !$hasFeedback;
            });

            Log::info('Action plans needing feedback', [
                'count' => $needFeedback->count(),
                'plans' => $needFeedback->map(function($plan) {
                    return [
                        'id' => $plan->id,
                        'title' => $plan->title,
                        'type' => $plan->type
                    ];
                })->toArray()
            ]);

            return response()->json($needFeedback->values());
        } catch (\Exception $e) {
            Log::error('Error getting completed events', [
                'scientist_id' => Auth::id() ?? 'not authenticated',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Failed to get completed events: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get all feedback provided by the scientist.
     */
    public function getAllFeedback()
    {
        try {
            $feedback = ScientistEventFeedback::with('actionPlan')
                ->where('scientist_id', Auth::id())
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json($feedback);
        } catch (\Exception $e) {
            Log::error('Error getting feedback: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get feedback'], 500);
        }
    }

    /**
     * Submit feedback for an event.
     */
    public function submitFeedback(Request $request)
    {
        try {
            Log::info('Raw feedback submission data:', [
                'data' => $request->all()
            ]);

            $request->validate([
                'action_plan_id' => 'required|exists:action_plans,id',
                'success_rating' => 'required|integer|min:1|max:5',
                'event_success_rating' => 'required|integer|min:1|max:5',
                'challenges' => 'required|string',
                'challenges_faced' => 'required|string',
                'suggestions' => 'required|string',
                'improvement_suggestions' => 'required|string',
                'objectives_met' => 'required|array',
                'objectives_met_list' => 'required|array',
                'learning_outcome' => 'required|string',
                'self_assessed_learning' => 'required|string',
            ]);

            Log::info('Feedback submission request received', [
                'scientist_id' => Auth::id(),
                'action_plan_id' => $request->action_plan_id,
                'success_rating' => $request->success_rating
            ]);

            // Verify the action plan belongs to the scientist and is completed
            $actionPlan = ActionPlan::where('id', $request->action_plan_id)
                ->where('scientist_id', Auth::id())
                ->where('status', 'completed')
                ->first();

            if (!$actionPlan) {
                Log::warning('Feedback submission failed: Action plan not found or not completed', [
                    'scientist_id' => Auth::id(),
                    'action_plan_id' => $request->action_plan_id
                ]);
                return response()->json(['error' => 'Action plan not found or not completed'], 404);
            }

            // Check if feedback already exists
            $existingFeedback = ScientistEventFeedback::where('action_plan_id', $request->action_plan_id)->first();
            if ($existingFeedback) {
                Log::warning('Feedback submission failed: Feedback already exists', [
                    'scientist_id' => Auth::id(),
                    'action_plan_id' => $request->action_plan_id,
                    'existing_feedback_id' => $existingFeedback->id
                ]);
                return response()->json(['error' => 'Feedback already submitted for this action plan'], 400);
            }

            // Create feedback
            Log::info('Creating feedback with data', [
                'action_plan_id' => $request->action_plan_id,
                'scientist_id' => Auth::id(),
                'success_rating' => $request->success_rating,
                'event_success_rating' => $request->event_success_rating,
                'challenges' => $request->challenges,
                'challenges_faced' => $request->challenges_faced,
                'suggestions' => $request->suggestions,
                'improvement_suggestions' => $request->improvement_suggestions,
                'objectives_met' => $request->objectives_met,
                'objectives_met_list' => $request->objectives_met_list,
                'learning_outcome' => $request->learning_outcome,
                'self_assessed_learning' => $request->self_assessed_learning,
            ]);

            try {
                $feedback = ScientistEventFeedback::create([
                    'action_plan_id' => $request->action_plan_id,
                    'scientist_id' => Auth::id(),
                    'success_rating' => $request->success_rating,
                    'event_success_rating' => $request->event_success_rating,
                    'challenges' => $request->challenges,
                    'challenges_faced' => $request->challenges_faced,
                    'suggestions' => $request->suggestions,
                    'improvement_suggestions' => $request->improvement_suggestions,
                    'objectives_met' => $request->objectives_met,
                    'objectives_met_list' => $request->objectives_met_list,
                    'learning_outcome' => $request->learning_outcome,
                    'self_assessed_learning' => $request->self_assessed_learning,
                ]);

                Log::info('Feedback created successfully', [
                    'feedback_id' => $feedback->id
                ]);
            } catch (\Exception $e) {
                Log::error('Error creating feedback', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e;
            }

            Log::info('Feedback submitted successfully', [
                'scientist_id' => Auth::id(),
                'action_plan_id' => $request->action_plan_id,
                'feedback_id' => $feedback->id
            ]);

            return response()->json([
                'message' => 'Feedback submitted successfully',
                'feedback' => $feedback
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::warning('Feedback validation failed', [
                'scientist_id' => Auth::id(),
                'errors' => $e->errors()
            ]);
            return response()->json([
                'error' => 'Validation failed: ' . implode(', ', array_map(function($arr) {
                    return implode(', ', $arr);
                }, $e->errors()))
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error submitting feedback', [
                'scientist_id' => Auth::id(),
                'action_plan_id' => $request->action_plan_id ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Failed to submit feedback: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get feedback details.
     */
    public function getFeedback($id)
    {
        try {
            $feedback = ScientistEventFeedback::with('actionPlan')
                ->where('id', $id)
                ->where('scientist_id', Auth::id())
                ->first();

            if (!$feedback) {
                return response()->json(['error' => 'Feedback not found'], 404);
            }

            return response()->json($feedback);
        } catch (\Exception $e) {
            Log::error('Error getting feedback details: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get feedback details'], 500);
        }
    }

    /**
     * Update all completed events with participant data.
     */
    public function updateCompletedEvents()
    {
        try {
            $scientistId = Auth::id();

            // Get all completed events for this scientist
            $completedEvents = ActionPlan::where('scientist_id', $scientistId)
                ->where('status', 'completed')
                ->get();

            $updatedCount = 0;
            foreach ($completedEvents as $event) {
                // Only update events that don't have participant data
                if (!$event->male_participants && !$event->female_participants) {
                    $event->update([
                        'male_participants' => 10,
                        'female_participants' => 15,
                        'transgender_participants' => 2,
                        'st_participants' => 5,
                        'sc_participants' => 8,
                        'general_participants' => 10,
                        'obc_participants' => 4,
                    ]);
                    $updatedCount++;
                }
            }

            return response()->json([
                'message' => "Successfully updated {$updatedCount} events with participant data",
                'updated_count' => $updatedCount
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating completed events: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to update completed events: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Verify and fix event statuses.
     */
    public function verifyEventStatuses()
    {
        try {
            $scientistId = Auth::id();

            // Get all events for this scientist
            $events = ActionPlan::where('scientist_id', $scientistId)->get();

            $fixedCount = 0;
            $issues = [];

            foreach ($events as $event) {
                $hasParticipantData = $event->male_participants || $event->female_participants || $event->transgender_participants;

                // If event has participant data but is not marked as completed
                if ($hasParticipantData && $event->status !== 'completed') {
                    $event->status = 'completed';
                    $event->save();
                    $fixedCount++;
                    $issues[] = "Event {$event->id} ({$event->title}) was marked as completed due to having participant data";
                }

                // If event is marked as completed but has no participant data
                if ($event->status === 'completed' && !$hasParticipantData) {
                    $event->update([
                        'male_participants' => 10,
                        'female_participants' => 15,
                        'transgender_participants' => 2,
                        'st_participants' => 5,
                        'sc_participants' => 8,
                        'general_participants' => 10,
                        'obc_participants' => 4,
                    ]);
                    $fixedCount++;
                    $issues[] = "Event {$event->id} ({$event->title}) was updated with participant data";
                }
            }

            return response()->json([
                'message' => "Successfully verified and fixed {$fixedCount} events",
                'fixed_count' => $fixedCount,
                'issues' => $issues
            ]);
        } catch (\Exception $e) {
            Log::error('Error verifying event statuses: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to verify event statuses: ' . $e->getMessage()], 500);
        }
    }
}
