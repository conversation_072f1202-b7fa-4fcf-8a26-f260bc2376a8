<?php

namespace App\Http\Controllers\Scientist;

use App\Http\Controllers\Controller;
use App\Models\ActionPlan;
use App\Models\StateData;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class CreateTestActionPlanController extends Controller
{
    /**
     * Create a test action plan with completed status.
     */
    public function createTestActionPlan()
    {
        try {
            $scientist = Auth::user();

            if (!$scientist || $scientist->role !== 'scientist') {
                return response()->json(['error' => 'You must be logged in as a scientist to create an action plan'], 403);
            }

            // Find or create a district for the scientist
            $district = StateData::where('scientist', $scientist->email)->first();

            if (!$district) {
                Log::info('Creating district for scientist', [
                    'scientist_id' => $scientist->id,
                    'scientist_email' => $scientist->email
                ]);

                $district = StateData::create([
                    'state' => 'Test State',
                    'district' => 'Test District',
                    'state_name' => 'Test State',
                    'district_name' => 'Test District',
                    'scientist' => $scientist->email,
                    'status' => 'pre-cocoon',
                ]);
            }

            // Log the scientist ID for debugging
            Log::info('Creating action plan for scientist', [
                'scientist_id' => $scientist->id,
                'scientist_email' => $scientist->email,
                'scientist_name' => $scientist->name
            ]);

            // Create a new action plan with completed status
            $actionPlan = new ActionPlan();
            $actionPlan->scientist_id = $scientist->id;
            $actionPlan->district_id = $district->id;
            $actionPlan->type = 'training';
            $actionPlan->title = 'Silk Production Training Workshop - ' . date('Y-m-d H:i:s');
            $actionPlan->location = 'Silk Research Center, Bangalore';
            $actionPlan->planned_date = now()->subDays(7); // 7 days ago
            $actionPlan->expected_participants = 50;
            $actionPlan->required_coordinators = null;
            $actionPlan->status = 'completed';
            $actionPlan->male_participants = 25;
            $actionPlan->female_participants = 20;
            $actionPlan->transgender_participants = 5;
            $actionPlan->st_participants = 10;
            $actionPlan->sc_participants = 15;
            $actionPlan->general_participants = 20;
            $actionPlan->obc_participants = 5;
            $actionPlan->gis_location = '12.9716,77.5946'; // Bangalore coordinates
            $actionPlan->save();

            Log::info('Created test action plan with ID: ' . $actionPlan->id, [
                'scientist_id' => $scientist->id,
                'action_plan_id' => $actionPlan->id,
                'title' => $actionPlan->title,
                'status' => $actionPlan->status
            ]);

            Log::info('Test action plan created successfully', [
                'action_plan_id' => $actionPlan->id,
                'scientist_id' => $scientist->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Test action plan created successfully',
                'action_plan' => [
                    'id' => $actionPlan->id,
                    'title' => $actionPlan->title,
                    'status' => $actionPlan->status,
                    'total_participants' => $actionPlan->male_participants + $actionPlan->female_participants + $actionPlan->transgender_participants
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating test action plan', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to create test action plan: ' . $e->getMessage()
            ], 500);
        }
    }
}
