<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class Scientist extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'district_id',
        'password',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    public function district()
    {
        return $this->belongsTo(District::class);
    }

    public function actionPlans()
    {
        return $this->hasMany(ActionPlan::class);
    }
}
