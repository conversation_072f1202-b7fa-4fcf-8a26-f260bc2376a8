<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Http\Request;
use App\Http\Controllers\SuperAdmin\CustomFormBuilderController;
use App\Http\Middleware\SanitizeInput;

echo "=== FORM BUILDER SYSTEM TESTING ===\n\n";

// Initialize Laravel app
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

class FormBuilderTester
{
    private $testResults = [];
    
    public function runAllTests()
    {
        echo "🚀 Starting Form Builder System Tests...\n\n";
        
        $this->testFormStructureValidation();
        $this->testJSONSanitization();
        $this->testFormFieldValidation();
        $this->testFileUploadValidation();
        $this->testFormPermissions();
        $this->testErrorHandling();
        
        $this->printSummary();
    }
    
    public function testFormStructureValidation()
    {
        echo "🧪 Testing Form Structure Validation...\n";
        
        $testCases = [
            'Valid Form Structure' => [
                'form_name' => 'Test Form',
                'target_user' => 'all_scientists',
                'form_structure' => '[{"type":"text","label":"Name","required":true},{"type":"email","label":"Email","required":false}]',
                'expected' => 'valid'
            ],
            'Invalid JSON' => [
                'form_name' => 'Invalid JSON Form',
                'target_user' => 'all_scientists',
                'form_structure' => '{"invalid": json}',
                'expected' => 'invalid'
            ],
            'Empty Array' => [
                'form_name' => 'Empty Form',
                'target_user' => 'all_scientists',
                'form_structure' => '[]',
                'expected' => 'invalid'
            ],
            'Missing Field Properties' => [
                'form_name' => 'Missing Props Form',
                'target_user' => 'all_scientists',
                'form_structure' => '[{"type":"text"}]',
                'expected' => 'invalid'
            ]
        ];
        
        $passedTests = 0;
        $totalTests = count($testCases);
        
        foreach ($testCases as $testName => $testData) {
            try {
                $request = new Request();
                $request->merge($testData);
                
                $controller = new CustomFormBuilderController();
                $response = $controller->createForm($request);
                
                $statusCode = $response->getStatusCode();
                $responseData = $response->getData(true);
                
                if ($testData['expected'] === 'valid' && $statusCode === 200) {
                    echo "   ✅ {$testName}: Valid form accepted\n";
                    $passedTests++;
                } elseif ($testData['expected'] === 'invalid' && $statusCode !== 200) {
                    echo "   ✅ {$testName}: Invalid form rejected\n";
                    $passedTests++;
                } else {
                    echo "   ❌ {$testName}: Unexpected result (Status: {$statusCode})\n";
                    if (isset($responseData['error'])) {
                        echo "      Error: {$responseData['error']}\n";
                    }
                }
                
            } catch (Exception $e) {
                if ($testData['expected'] === 'invalid') {
                    echo "   ✅ {$testName}: Exception caught as expected\n";
                    $passedTests++;
                } else {
                    echo "   ❌ {$testName}: Unexpected exception - {$e->getMessage()}\n";
                }
            }
        }
        
        $this->testResults['form_structure_validation'] = $passedTests === $totalTests;
        echo "   📊 Passed: {$passedTests}/{$totalTests}\n\n";
    }
    
    public function testJSONSanitization()
    {
        echo "🧪 Testing JSON Sanitization (SanitizeInput Middleware)...\n";
        
        $testCases = [
            'Valid JSON with Quotes' => [
                'form_structure' => '[{"type":"text","label":"Name with \"quotes\"","required":true}]',
                'expected' => 'preserved'
            ],
            'JSON with Special Characters' => [
                'form_structure' => '[{"type":"select","label":"Department","options":["R&D","Sales & Marketing"]}]',
                'expected' => 'preserved'
            ],
            'Regular Text Field' => [
                'description' => 'This is a <script>alert("xss")</script> test',
                'expected' => 'sanitized'
            ]
        ];
        
        $passedTests = 0;
        $totalTests = count($testCases);
        
        foreach ($testCases as $testName => $testData) {
            try {
                $request = Request::create('/test', 'POST', $testData);
                $middleware = new SanitizeInput();
                
                $response = $middleware->handle($request, function ($req) {
                    return response()->json(['processed' => true]);
                });
                
                $sanitizedInput = $request->all();
                
                if (isset($testData['form_structure'])) {
                    $decoded = json_decode($sanitizedInput['form_structure'], true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        echo "   ✅ {$testName}: JSON structure preserved\n";
                        $passedTests++;
                    } else {
                        echo "   ❌ {$testName}: JSON structure corrupted\n";
                    }
                } elseif (isset($testData['description'])) {
                    if (strpos($sanitizedInput['description'], '<script>') === false) {
                        echo "   ✅ {$testName}: HTML properly sanitized\n";
                        $passedTests++;
                    } else {
                        echo "   ❌ {$testName}: HTML not properly sanitized\n";
                    }
                }
                
            } catch (Exception $e) {
                echo "   ❌ {$testName}: Exception - {$e->getMessage()}\n";
            }
        }
        
        $this->testResults['json_sanitization'] = $passedTests === $totalTests;
        echo "   📊 Passed: {$passedTests}/{$totalTests}\n\n";
    }
    
    public function testFormFieldValidation()
    {
        echo "🧪 Testing Form Field Validation...\n";
        
        $validFieldTypes = ['text', 'email', 'number', 'textarea', 'select', 'checkbox', 'radio', 'file', 'date'];
        $passedTests = 0;
        $totalTests = count($validFieldTypes);
        
        foreach ($validFieldTypes as $fieldType) {
            $formStructure = json_encode([
                [
                    'type' => $fieldType,
                    'label' => "Test {$fieldType} Field",
                    'required' => true
                ]
            ]);
            
            try {
                $request = new Request();
                $request->merge([
                    'form_name' => "Test {$fieldType} Form",
                    'target_user' => 'all_scientists',
                    'form_structure' => $formStructure
                ]);
                
                $controller = new CustomFormBuilderController();
                $response = $controller->createForm($request);
                
                if ($response->getStatusCode() === 200) {
                    echo "   ✅ Field type '{$fieldType}': Accepted\n";
                    $passedTests++;
                } else {
                    echo "   ❌ Field type '{$fieldType}': Rejected\n";
                }
                
            } catch (Exception $e) {
                echo "   ❌ Field type '{$fieldType}': Exception - {$e->getMessage()}\n";
            }
        }
        
        $this->testResults['field_validation'] = $passedTests === $totalTests;
        echo "   📊 Passed: {$passedTests}/{$totalTests}\n\n";
    }
    
    public function testFileUploadValidation()
    {
        echo "🧪 Testing File Upload Validation...\n";
        
        try {
            // Test file field structure
            $formStructure = json_encode([
                [
                    'type' => 'file',
                    'label' => 'Upload Document',
                    'required' => false,
                    'accept' => '.pdf,.doc,.docx'
                ]
            ]);
            
            $request = new Request();
            $request->merge([
                'form_name' => 'File Upload Test Form',
                'target_user' => 'all_scientists',
                'form_structure' => $formStructure
            ]);
            
            $controller = new CustomFormBuilderController();
            $response = $controller->createForm($request);
            
            if ($response->getStatusCode() === 200) {
                echo "   ✅ File upload field structure: Valid\n";
                $this->testResults['file_upload_validation'] = true;
            } else {
                echo "   ❌ File upload field structure: Invalid\n";
                $this->testResults['file_upload_validation'] = false;
            }
            
        } catch (Exception $e) {
            echo "   ❌ File upload validation exception: {$e->getMessage()}\n";
            $this->testResults['file_upload_validation'] = false;
        }
        
        echo "\n";
    }
    
    public function testFormPermissions()
    {
        echo "🧪 Testing Form Permissions...\n";
        
        $targetUsers = ['all_scientists', 'pre_cocoon', 'post_cocoon'];
        $passedTests = 0;
        $totalTests = count($targetUsers);
        
        foreach ($targetUsers as $targetUser) {
            try {
                $request = new Request();
                $request->merge([
                    'form_name' => "Permission Test {$targetUser}",
                    'target_user' => $targetUser,
                    'form_structure' => '[{"type":"text","label":"Test","required":true}]'
                ]);
                
                $controller = new CustomFormBuilderController();
                $response = $controller->createForm($request);
                
                if ($response->getStatusCode() === 200) {
                    echo "   ✅ Target user '{$targetUser}': Valid\n";
                    $passedTests++;
                } else {
                    echo "   ❌ Target user '{$targetUser}': Invalid\n";
                }
                
            } catch (Exception $e) {
                echo "   ❌ Target user '{$targetUser}': Exception - {$e->getMessage()}\n";
            }
        }
        
        $this->testResults['form_permissions'] = $passedTests === $totalTests;
        echo "   📊 Passed: {$passedTests}/{$totalTests}\n\n";
    }
    
    public function testErrorHandling()
    {
        echo "🧪 Testing Error Handling...\n";
        
        $errorCases = [
            'Missing Form Name' => [
                'target_user' => 'all_scientists',
                'form_structure' => '[{"type":"text","label":"Test","required":true}]'
            ],
            'Invalid Target User' => [
                'form_name' => 'Error Test Form',
                'target_user' => 'invalid_user_type',
                'form_structure' => '[{"type":"text","label":"Test","required":true}]'
            ],
            'Missing Form Structure' => [
                'form_name' => 'Error Test Form',
                'target_user' => 'all_scientists'
            ]
        ];
        
        $passedTests = 0;
        $totalTests = count($errorCases);
        
        foreach ($errorCases as $testName => $testData) {
            try {
                $request = new Request();
                $request->merge($testData);
                
                $controller = new CustomFormBuilderController();
                $response = $controller->createForm($request);
                
                if ($response->getStatusCode() === 422) {
                    echo "   ✅ {$testName}: Properly rejected with validation error\n";
                    $passedTests++;
                } else {
                    echo "   ❌ {$testName}: Not properly rejected (Status: {$response->getStatusCode()})\n";
                }
                
            } catch (Exception $e) {
                echo "   ✅ {$testName}: Exception caught as expected\n";
                $passedTests++;
            }
        }
        
        $this->testResults['error_handling'] = $passedTests === $totalTests;
        echo "   📊 Passed: {$passedTests}/{$totalTests}\n\n";
    }
    
    public function printSummary()
    {
        echo "📊 FORM BUILDER TESTING SUMMARY\n";
        echo str_repeat("=", 50) . "\n";
        
        $totalTests = count($this->testResults);
        $passedTests = array_sum($this->testResults);
        
        foreach ($this->testResults as $test => $result) {
            $status = $result ? "✅ PASS" : "❌ FAIL";
            echo sprintf("%-30s %s\n", ucwords(str_replace('_', ' ', $test)), $status);
        }
        
        echo str_repeat("-", 50) . "\n";
        echo sprintf("Total Tests: %d | Passed: %d | Failed: %d\n", 
                    $totalTests, $passedTests, $totalTests - $passedTests);
        
        $percentage = $totalTests > 0 ? round(($passedTests / $totalTests) * 100, 2) : 0;
        echo sprintf("Success Rate: %s%%\n", $percentage);
        
        if ($percentage >= 80) {
            echo "🎉 Form Builder system is functioning well!\n";
        } elseif ($percentage >= 60) {
            echo "⚠️  Form Builder system has some issues that need attention.\n";
        } else {
            echo "🚨 Form Builder system has significant issues that require immediate attention.\n";
        }
    }
}

// Run the tests
$tester = new FormBuilderTester();
$tester->runAllTests();

echo "\n=== FORM BUILDER TESTING COMPLETE ===\n";
