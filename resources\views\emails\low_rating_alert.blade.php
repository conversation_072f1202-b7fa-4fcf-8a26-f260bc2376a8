<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Low Rating Alert</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
        }
        .header {
            background-color: #dc3545;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        .footer {
            background-color: #f5f5f5;
            padding: 10px 20px;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
        .button {
            display: inline-block;
            background-color: #4a6fdc;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 20px;
        }
        .alert-details {
            background-color: #fff3f3;
            border: 1px solid #ffcccb;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .rating {
            font-weight: bold;
            font-size: 24px;
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Low Rating Alert</h1>
    </div>
    <div class="content">
        <h2>Attention Required: Low Rating Detected</h2>
        <p>A low rating has been detected for the event "{{ $event_title }}".</p>
        
        <div class="alert-details">
            <p><strong>Rating Type:</strong> {{ $rating_type }}</p>
            <p><strong>Rating Value:</strong> <span class="rating">{{ $rating_value }}</span></p>
            <p><strong>Threshold:</strong> {{ $threshold }}</p>
            <p><strong>Alert Generated On:</strong> {{ $alert_date }}</p>
        </div>
        
        <p>This alert is generated when ratings fall below the configured threshold. Please review the feedback and take appropriate action.</p>
        <a href="{{ $dashboard_url }}" class="button">View Analytics Dashboard</a>
    </div>
    <div class="footer">
        <p>&copy; {{ date('Y') }} Mera Resham Mera Abhimaan. All rights reserved.</p>
    </div>
</body>
</html>
