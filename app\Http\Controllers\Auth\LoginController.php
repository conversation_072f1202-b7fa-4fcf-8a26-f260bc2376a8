<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LoginController extends Controller
{
    /**
     * Show the login form.
     */
    public function showLoginForm()
    {
        // If user is already logged in, redirect to appropriate dashboard
        if (Auth::check()) {
            if (Auth::user()->role === 'super_admin') {
                return redirect()->route('super-admin.dashboard');
            } else if (Auth::user()->role === 'admin') {
                return redirect()->route('admin.dashboard');
            } else if (Auth::user()->role === 'scientist') {
                return redirect()->route('scientist.dashboard');
            } else if (Auth::user()->role === 'zonal_coordinator') {
                return redirect()->route('zonal-coordinator.dashboard');
            } else if (Auth::user()->role === 'district_state_coordinator') {
                return redirect()->route('district-state-coordinator.dashboard');
            } else {
                return redirect()->route('user.dashboard');
            }
        }

        // Otherwise show the login form
        return view('auth.simple-login');
    }

    /**
     * Handle a login request to the application.
     */
    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required'],
        ]);
        // dd($credentials, \App\Models\User::where('email', $credentials['email'])->first());

        if (Auth::attempt($credentials)) {
            $request->session()->regenerate();

            // Redirect based on user role
            if (Auth::user()->role === 'super_admin') {
                return redirect()->intended('super-admin/dashboard');
            } else if (Auth::user()->role === 'admin') {
                return redirect()->intended('admin/dashboard');
            } else if (Auth::user()->role === 'scientist') {
                return redirect()->intended('scientist/dashboard');
            } else if (Auth::user()->role === 'zonal_coordinator') {
                return redirect()->intended('zonal-coordinator/dashboard');
            } else if (Auth::user()->role === 'district_state_coordinator') {
                return redirect()->intended('district-state-coordinator/dashboard');
            } else {
                return redirect()->intended('user/dashboard');
            }
        }

        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ])->onlyInput('email');
    }

    /**
     * Log the user out of the application.
     */
    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('login');
    }
}
