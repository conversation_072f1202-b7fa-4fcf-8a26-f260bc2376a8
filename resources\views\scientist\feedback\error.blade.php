@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>{{ __('Error') }}</span>
                        <div>
                            <a href="{{ route('scientist.feedback-import.index') }}" class="btn btn-sm btn-secondary">Back to Review System</a>
                        </div>
                    </div>
                </div>

                <div class="card-body text-center">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                        <h4>Oops! Something went wrong</h4>
                        <p>{{ $message ?? 'An unexpected error occurred.' }}</p>
                    </div>

                    <div class="mt-4">
                        <a href="{{ route('scientist.feedback-import.index') }}" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i> Go Back to Review System
                        </a>
                        <a href="{{ route('scientist.dashboard') }}" class="btn btn-secondary">
                            <i class="fas fa-home"></i> Go to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
