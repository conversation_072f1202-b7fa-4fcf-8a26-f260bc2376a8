@extends('layouts.public')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">Feedback for {{ $actionPlan->title }}</div>

                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul>
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('feedback.submit', $actionPlan->id) }}">
                        @csrf

                        <div class="form-group mb-3">
                            <label for="name">Name</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>

                        <div class="form-group mb-3">
                            <label for="contact">Contact (Phone Number)</label>
                            <input type="text" class="form-control" id="contact" name="contact" required>
                        </div>

                        <div class="form-group mb-3">
                            <label>Event Benefit Rating</label>
                            <div class="rating-buttons">
                                @for ($i = 1; $i <= 5; $i++)
                                    <button type="button" class="btn btn-outline-primary rating-btn" data-rating="{{ $i }}" onclick="setRating('benefit', {{ $i }})">{{ $i }}</button>
                                @endfor
                            </div>
                            <input type="hidden" name="benefit_rating" id="benefit_rating" required>
                        </div>

                        <div class="form-group mb-3">
                            <label>Speaker Clarity Rating</label>
                            <div class="rating-buttons">
                                @for ($i = 1; $i <= 5; $i++)
                                    <button type="button" class="btn btn-outline-primary rating-btn" data-rating="{{ $i }}" onclick="setRating('speaker', {{ $i }})">{{ $i }}</button>
                                @endfor
                            </div>
                            <input type="hidden" name="speaker_rating" id="speaker_rating" required>
                        </div>

                        <div class="form-group mb-3">
                            <label>Would you recommend this program?</label>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-success" onclick="setRecommend(true)">Yes</button>
                                <button type="button" class="btn btn-outline-danger" onclick="setRecommend(false)">No</button>
                            </div>
                            <input type="hidden" name="would_recommend" id="would_recommend" required>
                        </div>

                        <div class="form-group mb-3">
                            <label for="most_helpful_topic">Most Helpful Topic</label>
                            <input type="text" class="form-control" id="most_helpful_topic" name="most_helpful_topic" required>
                        </div>

                        <div class="form-group mb-3">
                            <label for="suggestions">Suggestions for Improvement</label>
                            <textarea class="form-control" id="suggestions" name="suggestions" rows="3"></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary">Submit Feedback</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function setRating(type, value) {
    document.getElementById(type + '_rating').value = value;
    const buttons = document.querySelectorAll(`[data-rating-type="${type}"] .rating-btn`);
    buttons.forEach(btn => {
        btn.classList.remove('active');
        if (parseInt(btn.dataset.rating) <= value) {
            btn.classList.add('active');
        }
    });
}

function setRecommend(value) {
    document.getElementById('would_recommend').value = value;
    const yesBtn = document.querySelector('.btn-outline-success');
    const noBtn = document.querySelector('.btn-outline-danger');
    if (value) {
        yesBtn.classList.add('active');
        noBtn.classList.remove('active');
    } else {
        yesBtn.classList.remove('active');
        noBtn.classList.add('active');
    }
}
</script>
@endpush
@endsection
