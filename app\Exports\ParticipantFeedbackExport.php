<?php

namespace App\Exports;

use App\Models\ActionPlanFeedback;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ParticipantFeedbackExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $eventId;
    protected $startDate;
    protected $endDate;
    protected $districtIds;

    /**
     * Create a new export instance.
     *
     * @param array $params
     */
    public function __construct(array $params = [])
    {
        $this->eventId = $params['event_id'] ?? null;
        $this->startDate = $params['start_date'] ?? null;
        $this->endDate = $params['end_date'] ?? null;
        $this->districtIds = $params['district_ids'] ?? [];
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = ActionPlanFeedback::with(['actionPlan:id,title,planned_date,district_id', 'actionPlan.district:id,district,state']);

        // Filter by event if specified
        if ($this->eventId) {
            $query->where('action_plan_id', $this->eventId);
        }

        // Filter by date range if specified
        if ($this->startDate && $this->endDate) {
            $query->whereHas('actionPlan', function ($q) {
                $q->whereBetween('planned_date', [$this->startDate, $this->endDate]);
            });
        }

        // Filter by districts if specified
        if (!empty($this->districtIds)) {
            $query->whereHas('actionPlan', function ($q) {
                $q->whereIn('district_id', $this->districtIds);
            });
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            'Action Plan Title',
            'Action Plan Date',
            'District',
            'State',
            'Event Success Rating (1-10)',
            'Challenges Faced',
            'Suggestions for Improvement',
            'Objectives Met',
            'Self Assessed Learning Outcome',
            'Male Participants',
            'Female Participants',
            'Transgender Participants',
            'ST Participants',
            'SC Participants',
            'General Participants',
            'OBC Participants',
            'GIS Location',
            'Submission Date',
        ];
    }

    /**
     * @param mixed $row
     *
     * @return array
     */
    public function map($row): array
    {
        return [
            $row->id,
            $row->actionPlan->title ?? 'N/A',
            $row->actionPlan->planned_date ? date('Y-m-d', strtotime($row->actionPlan->planned_date)) : 'N/A',
            $row->actionPlan->district->district ?? 'N/A',
            $row->actionPlan->district->state ?? 'N/A',
            $row->event_success_rating ?? 'N/A',
            $row->challenges_faced ?? 'N/A',
            $row->suggestions_for_improvement ?? 'N/A',
            is_array($row->objectives_met) ? implode(', ', $row->objectives_met) : 'N/A',
            $row->self_assessed_learning_outcome ?? 'N/A',
            $row->male_participants ?? 0,
            $row->female_participants ?? 0,
            $row->transgender_participants ?? 0,
            $row->st_participants ?? 0,
            $row->sc_participants ?? 0,
            $row->general_participants ?? 0,
            $row->obc_participants ?? 0,
            $row->gis_location ?? 'N/A',
            date('Y-m-d H:i', strtotime($row->created_at)),
        ];
    }

    /**
     * @param Worksheet $sheet
     *
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold
            1 => ['font' => ['bold' => true]],
        ];
    }
}
