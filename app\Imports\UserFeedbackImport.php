<?php

namespace App\Imports;

use App\Models\UserFeedback;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class UserFeedbackImport implements ToModel, WithHeadingRow
{
    public function model(array $row)
    {
        // Check if feedback already exists for this contact and action plan
        $existingFeedback = UserFeedback::where('contact', $row['contact'])
            ->where('action_plan_id', $row['action_plan_id'])
            ->first();

        if ($existingFeedback) {
            return null; // Skip this row
        }

        return new UserFeedback([
            'action_plan_id' => $row['action_plan_id'],
            'name' => $row['name'],
            'contact' => $row['contact'],
            'benefit_rating' => $row['benefit_rating'],
            'would_recommend' => strtolower($row['would_recommend']) === 'yes' ? 1 : 0,
            'most_helpful_topic' => $row['most_helpful_topic'],
            'speaker_rating' => $row['speaker_rating'],
            'improvement_suggestions' => $row['improvement_suggestions'],
            'source' => 'excel'
        ]);
    }
}