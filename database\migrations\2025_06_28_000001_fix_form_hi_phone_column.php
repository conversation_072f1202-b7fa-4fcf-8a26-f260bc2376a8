<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        try {
            // Check if the form_hi table exists
            if (Schema::hasTable('form_hi')) {
                // Get current column information
                $columns = DB::select("SHOW COLUMNS FROM form_hi WHERE Field = 'phone_no'");
                
                if (!empty($columns)) {
                    $phoneColumn = $columns[0];
                    Log::info("Current phone_no column type in form_hi: {$phoneColumn->Type}");
                    
                    // Check if it's still an integer type
                    if (stripos($phoneColumn->Type, 'int') !== false) {
                        Log::info("Changing phone_no column from {$phoneColumn->Type} to VARCHAR(20)");
                        
                        // Change the column type to VARCHAR
                        DB::statement("ALTER TABLE form_hi MODIFY phone_no VARCHAR(20)");
                        
                        Log::info("Successfully changed phone_no column to VARCHAR(20) in form_hi table");
                    } else {
                        Log::info("phone_no column is already VARCHAR type: {$phoneColumn->Type}");
                    }
                } else {
                    Log::warning("phone_no column not found in form_hi table");
                }
            } else {
                Log::warning("form_hi table does not exist");
            }
        } catch (\Exception $e) {
            Log::error("Error fixing form_hi phone_no column: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // We don't want to revert back to an integer type as it would cause data loss
        Log::info("Not reverting phone_no column change to prevent data loss");
    }
};
