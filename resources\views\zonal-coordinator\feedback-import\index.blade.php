@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Action Plans - Public Feedback Management</h4>
                    <small class="text-muted">Manage public feedback collection for action plans in your districts</small>
                </div>

                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul>
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li><strong>Add Public Review:</strong> Generate QR codes for participants to submit feedback without login (available for 2 days after event date)</li>
                            <li><strong>View All Reviews:</strong> See all feedback submitted for each action plan</li>
                            <li><strong>Download Format:</strong> Get Excel template for bulk feedback upload</li>
                            <li><strong>Bulk Review Upload:</strong> Upload multiple feedback entries using the Excel format</li>
                        </ul>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th style="color: #000 !important;">Title</th>
                                    <th style="color: #000 !important;">Type</th>
                                    <th style="color: #000 !important;">Planned Date</th>
                                    <th style="color: #000 !important;">Location</th>
                                    <th style="color: #000 !important;">Scientist</th>
                                    <th style="color: #000 !important;">District</th>
                                    <th style="color: #000 !important;">Status</th>
                                    <th style="color: #000 !important;">Reviews</th>
                                    <th style="color: #000 !important;">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="eventsTableBody">
                                <tr>
                                    <td colspan="9" class="text-center">Loading action plans...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Load action plans
    function loadActionPlans() {
        $.ajax({
            url: '{{ route("zonal-coordinator.feedback-import.get-events") }}',
            method: 'GET',
            success: function(response) {
                if (response.error) {
                    $('#eventsTableBody').html('<tr><td colspan="9" class="text-center text-danger">' + response.error + '</td></tr>');
                    return;
                }

                if (!Array.isArray(response)) {
                    console.error('Invalid response format:', response);
                    $('#eventsTableBody').html('<tr><td colspan="9" class="text-center text-danger">Invalid response format</td></tr>');
                    return;
                }

                if (response.length === 0) {
                    $('#eventsTableBody').html('<tr><td colspan="9" class="text-center text-muted">No action plans found</td></tr>');
                    return;
                }

                let html = '';
                response.forEach(function(plan) {
                    // Status badge
                    const statusBadge = getStatusBadge(plan.status);

                    // Type badge
                    const typeBadge = getTypeBadge(plan.type);

                    // Add review button (only show if can add review)
                    const addReviewButton = plan.can_add_review
                        ? `<button onclick="showQRCode(${plan.id})" class="btn btn-primary btn-sm mb-1" title="Generate QR code for public reviews">
                             <i class="fas fa-qrcode"></i> Add Public Review
                           </button><br>`
                        : '';

                    html += `<tr>
                        <td style="color: #000 !important;"><strong>${plan.title || 'N/A'}</strong></td>
                        <td>${typeBadge}</td>
                        <td style="color: #000 !important;">${plan.planned_date || 'N/A'}</td>
                        <td style="color: #000 !important;">${plan.location || 'N/A'}</td>
                        <td style="color: #000 !important;">${plan.scientist || 'N/A'}</td>
                        <td style="color: #000 !important;">${plan.district || 'N/A'}</td>
                        <td>${statusBadge}</td>
                        <td><span class="badge bg-info text-dark">${plan.feedback_count || 0} reviews</span></td>
                        <td>
                            <a href="/zonal-coordinator/feedback-import/event-feedback/${plan.id}" class="btn btn-primary btn-sm mb-1" title="View all feedback for this action plan">
                                <i class="fas fa-eye"></i> View
                            </a><br>
                            ${addReviewButton}
                            <a href="/templates/user_feedback_template.csv" class="btn btn-secondary btn-sm mb-1" title="Download CSV template" download="user_feedback_template.csv">
                                <i class="fas fa-download"></i> Download Format
                            </a><br>
                            <button onclick="showBulkUpload(${plan.id})" class="btn btn-success btn-sm" title="Upload bulk reviews">
                                <i class="fas fa-upload"></i> Bulk Upload
                            </button>
                        </td>
                    </tr>`;
                });
                $('#eventsTableBody').html(html);
            },
            error: function(xhr, status, error) {
                console.error('Error loading action plans:', error);
                let errorMessage = 'Failed to load action plans';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMessage = xhr.responseJSON.error;
                }
                $('#eventsTableBody').html(`<tr><td colspan="9" class="text-center text-danger">${errorMessage}</td></tr>`);
            }
        });
    }

    // Helper function to get status badge
    function getStatusBadge(status) {
        switch(status) {
            case 'planned':
                return '<span class="badge bg-warning text-dark">Planned</span>';
            case 'completed':
                return '<span class="badge bg-success">Completed</span>';
            case 'cancelled':
                return '<span class="badge bg-danger">Cancelled</span>';
            default:
                return '<span class="badge bg-secondary">Unknown</span>';
        }
    }

    // Helper function to get type badge
    function getTypeBadge(type) {
        switch(type) {
            case 'training':
                return '<span class="badge bg-primary">Training</span>';
            case 'field_visit':
                return '<span class="badge bg-info">Field Visit</span>';
            case 'demonstration':
                return '<span class="badge bg-success">Demonstration</span>';
            case 'awareness':
                return '<span class="badge bg-warning text-dark">Awareness</span>';
            default:
                return '<span class="badge bg-secondary">Unknown</span>';
        }
    }

    // Initial load
    loadActionPlans();

    // Refresh every 5 minutes
    setInterval(loadActionPlans, 300000);
});

function showQRCode(planId) {
    // Show QR code modal
    $.ajax({
        url: `/zonal-coordinator/feedback-import/generate-qr/${planId}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                // Create and show modal with QR code
                const modalHtml = `
                    <div class="modal fade" id="qrCodeModal" tabindex="-1" role="dialog">
                        <div class="modal-dialog" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Scan QR Code for Public Review</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body text-center">
                                    <img src="${response.qr_code_url}" alt="QR Code" class="img-fluid">
                                    <p class="mt-3">Scan this QR code to submit a public review</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Remove existing modal if any
                $('#qrCodeModal').remove();

                // Add new modal to body and show it
                $('body').append(modalHtml);
                $('#qrCodeModal').modal('show');
            } else {
                alert('Failed to generate QR code. Please try again.');
            }
        },
        error: function() {
            alert('Failed to generate QR code. Please try again.');
        }
    });
}

function showBulkUpload(planId) {
    // Show bulk upload modal
    const modalHtml = `
        <div class="modal fade" id="bulkUploadModal" tabindex="-1" role="dialog" aria-labelledby="bulkUploadModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="bulkUploadModalLabel">Bulk Review Upload</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="bulkUploadForm" enctype="multipart/form-data">
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                            <input type="hidden" name="event_id" value="${planId}">
                            <div class="form-group">
                                <label for="excelFile">Select Excel File</label>
                                <input type="file" class="form-control-file" id="excelFile" name="excel_file" accept=".xlsx,.xls,.csv" required>
                            </div>
                            <button type="submit" class="btn btn-primary">Upload</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    $('#bulkUploadModal').remove();

    // Add new modal to body and show it
    $('body').append(modalHtml);
    $('#bulkUploadModal').modal('show');

    // Handle form submission
    $('#bulkUploadForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        $.ajax({
            url: '{{ route("zonal-coordinator.feedback-import.import") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    alert('Feedback imported successfully');
                    $('#bulkUploadModal').modal('hide');
                    // Reload the table
                    location.reload();
                } else {
                    alert(response.message || 'Failed to import feedback');
                }
            },
            error: function(xhr) {
                let errorMessage = 'Failed to import feedback';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                alert(errorMessage);
            }
        });
    });
}
</script>
@endpush
@endsection
