<?php

namespace App\Exports;

use App\Models\ActionPlanFeedback;
use App\Models\ActionPlan;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;

class ParticipantFeedbackPdfExport
{
    protected $eventId;
    protected $startDate;
    protected $endDate;
    protected $districtIds;
    protected $includeAnalytics;

    /**
     * Create a new export instance.
     *
     * @param array $params
     */
    public function __construct(array $params = [])
    {
        $this->eventId = $params['event_id'] ?? null;
        $this->startDate = $params['start_date'] ?? null;
        $this->endDate = $params['end_date'] ?? null;
        $this->districtIds = $params['district_ids'] ?? [];
        $this->includeAnalytics = $params['include_analytics'] ?? false;
    }

    /**
     * Get the view for the PDF export.
     *
     * @return View
     */
    public function view(): View
    {
        $data = $this->getData();

        return view('exports.participant-feedback-pdf', $data);
    }

    /**
     * Get data for the PDF export.
     *
     * @return array
     */
    protected function getData(): array
    {
        $query = ActionPlanFeedback::with(['actionPlan:id,title,planned_date,district_id', 'actionPlan.district:id,district,state']);

        // Filter by event if specified
        if ($this->eventId) {
            $query->where('action_plan_id', $this->eventId);
        }

        // Filter by date range if specified
        if ($this->startDate && $this->endDate) {
            $query->whereHas('actionPlan', function ($q) {
                $q->whereBetween('planned_date', [$this->startDate, $this->endDate]);
            });
        }

        // Filter by districts if specified
        if (!empty($this->districtIds)) {
            $query->whereHas('actionPlan', function ($q) {
                $q->whereIn('district_id', $this->districtIds);
            });
        }

        $feedback = $query->orderBy('created_at', 'desc')->get();

        $data = [
            'feedback' => $feedback,
            'include_analytics' => $this->includeAnalytics,
        ];

        // Add event details if filtering by a specific event
        if ($this->eventId) {
            $actionPlan = ActionPlan::with('district:id,district,state')->find($this->eventId);
            $data['action_plan'] = $actionPlan;
        }

        // Add analytics data if requested
        if ($this->includeAnalytics) {
            $data['analytics'] = $this->generateAnalytics($feedback);
        }

        return $data;
    }

    /**
     * Generate analytics data for the PDF export.
     *
     * @param Collection $feedback
     * @return array
     */
    protected function generateAnalytics(Collection $feedback): array
    {
        $analytics = [
            'total_feedback' => $feedback->count(),
            'avg_event_success_rating' => $feedback->avg('event_success_rating') ? round($feedback->avg('event_success_rating'), 1) : 0,
            'total_participants' => [
                'male' => $feedback->sum('male_participants'),
                'female' => $feedback->sum('female_participants'),
                'transgender' => $feedback->sum('transgender_participants'),
                'st' => $feedback->sum('st_participants'),
                'sc' => $feedback->sum('sc_participants'),
                'general' => $feedback->sum('general_participants'),
                'obc' => $feedback->sum('obc_participants'),
            ],
        ];

        // Calculate rating distribution
        $successDistribution = array_fill(1, 10, 0);

        foreach ($feedback as $item) {
            $successRating = $item->event_success_rating;
            if ($successRating >= 1 && $successRating <= 10) {
                $successDistribution[$successRating]++;
            }
        }

        $analytics['rating_distribution'] = [
            'success' => $successDistribution,
        ];

        // Analyze objectives met
        $objectives = [];
        foreach ($feedback as $item) {
            if (!empty($item->objectives_met) && is_array($item->objectives_met)) {
                foreach ($item->objectives_met as $objective) {
                    if (isset($objectives[$objective])) {
                        $objectives[$objective]++;
                    } else {
                        $objectives[$objective] = 1;
                    }
                }
            }
        }

        arsort($objectives);
        $analytics['top_objectives'] = array_slice($objectives, 0, 5, true);

        // Analyze challenges and suggestions
        $challenges = [];
        $suggestions = [];
        foreach ($feedback as $item) {
            if (!empty($item->challenges_faced)) {
                $challenges[] = $item->challenges_faced;
            }
            if (!empty($item->suggestions_for_improvement)) {
                $suggestions[] = $item->suggestions_for_improvement;
            }
        }

        $analytics['challenges'] = array_slice($challenges, 0, 5);
        $analytics['suggestions'] = array_slice($suggestions, 0, 5);

        return $analytics;
    }
}
