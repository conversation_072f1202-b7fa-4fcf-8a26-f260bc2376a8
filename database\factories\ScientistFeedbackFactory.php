<?php

namespace Database\Factories;

use App\Models\ScientistFeedback;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ScientistFeedback>
 */
class ScientistFeedbackFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ScientistFeedback::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'scientist_id' => User::factory()->create(['role' => 'scientist'])->id,
            'overall_rating' => $this->faker->numberBetween(1, 5),
            'preparedness_rating' => $this->faker->numberBetween(1, 5),
            'communication_rating' => $this->faker->numberBetween(1, 5),
            'engagement_rating' => $this->faker->numberBetween(1, 5),
            'adherence_rating' => $this->faker->numberBetween(1, 5),
            'remarks' => $this->faker->optional()->paragraph(),
            'created_by' => User::factory()->create(['role' => 'super_admin'])->id,
        ];
    }
}
