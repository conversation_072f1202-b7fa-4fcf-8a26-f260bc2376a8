@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0" id="formTitle">Form</h5>
                    <a href="{{ route('scientist.forms') }}" class="btn btn-secondary">Back to List</a>
                </div>

                <div class="card-body">
                    <div id="formAlert" class="alert alert-info d-none">
                        <!-- Alert messages will be displayed here -->
                    </div>

                    <form id="formDisplay" enctype="multipart/form-data">
                        @csrf
                        <div class="row" id="formFields">
                            <!-- Form fields will be rendered here -->
                        </div>

                        <div class="text-end mt-3">
                            <button type="submit" class="btn btn-primary" id="submitBtn">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Get form ID from URL path
const pathSegments = window.location.pathname.split('/');
const formId = pathSegments[pathSegments.length - 2]; // Get the ID from the URL path

// Global variable to store form structure for submission
let globalFormStructure = [];

// Load form data when page loads
window.addEventListener('load', function() {
    if (formId) {
        fetch(`/scientist/forms/${formId}`)
            .then(response => response.json())
            .then(data => {
                document.getElementById('formTitle').textContent = data.form_name;

                // Parse the form structure with robust error handling
                let formStructure;
                try {
                    console.log('Raw form structure data:', data.form_structure);
                    console.log('Type of form structure:', typeof data.form_structure);

                    if (typeof data.form_structure === 'string') {
                        // Try direct JSON parse first
                        try {
                            formStructure = JSON.parse(data.form_structure);
                        } catch (parseError) {
                            console.warn('Direct JSON parse failed, trying to handle double encoding:', parseError);

                            // Try to handle double-encoded JSON
                            try {
                                const firstDecode = JSON.parse(data.form_structure);
                                if (typeof firstDecode === 'string') {
                                    formStructure = JSON.parse(firstDecode);
                                } else {
                                    formStructure = firstDecode;
                                }
                            } catch (doubleParseError) {
                                console.error('Double decode also failed:', doubleParseError);
                                throw new Error('Failed to parse JSON after multiple attempts');
                            }
                        }
                    } else if (Array.isArray(data.form_structure)) {
                        formStructure = data.form_structure;
                    } else if (data.form_structure && typeof data.form_structure === 'object') {
                        // If it's an object but not an array, wrap it in an array
                        formStructure = [data.form_structure];
                    } else {
                        throw new Error('Form structure is not in a recognized format');
                    }

                    // Ensure formStructure is an array
                    if (!Array.isArray(formStructure)) {
                        console.error('Form structure is not an array after parsing:', formStructure);
                        throw new Error('Form structure is not an array');
                    }

                    // Validate that the array contains valid field objects
                    if (formStructure.length === 0) {
                        console.warn('Form structure is empty');
                        alert('This form has no fields defined. Please contact the administrator.');
                        return;
                    }

                    // Validate each field has required properties
                    for (let i = 0; i < formStructure.length; i++) {
                        const field = formStructure[i];
                        if (!field.type || !field.label) {
                            console.error(`Field at index ${i} is missing required properties:`, field);
                            throw new Error(`Field at index ${i} is missing required properties (type or label)`);
                        }
                    }

                    console.log('Successfully parsed and validated form structure:', formStructure);

                    // Store form structure globally for use during submission
                    globalFormStructure = formStructure;
                } catch (error) {
                    console.error('Error parsing form structure:', error);
                    console.error('Original form structure data:', data.form_structure);
                    alert('Error loading form structure: ' + error.message + '. Please contact support.');
                    return;
                }

                renderForm(formStructure);
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while loading the form');
            });
    }
});

function renderForm(fields) {
    const formFields = document.getElementById('formFields');
    formFields.innerHTML = '';

    fields.forEach((field, index) => {
        let fieldHtml = '';

        switch(field.type) {
            case 'text':
                fieldHtml = renderTextField(field, index);
                break;
            case 'number':
                fieldHtml = renderNumberField(field, index);
                break;
            case 'email':
                fieldHtml = renderEmailField(field, index);
                break;
            case 'file':
                fieldHtml = renderFileField(field, index);
                break;
            case 'textarea':
                fieldHtml = renderTextareaField(field, index);
                break;
            case 'select':
                fieldHtml = renderSelectField(field, index);
                break;
            case 'checkbox':
                fieldHtml = renderCheckboxField(field, index);
                break;
            case 'radio':
                fieldHtml = renderRadioField(field, index);
                break;
            default:
                console.warn(`Unsupported field type: ${field.type}`);
                fieldHtml = renderTextField(field, index); // Fallback to text field
                break;
        }

        formFields.insertAdjacentHTML('beforeend', fieldHtml);
    });

    // Initialize conditional logic after all fields are rendered
    initializeConditionalLogic(fields);
}

function renderTextField(field, index) {
    return `
        <div class="mb-3 ${field.fullWidth ? 'col-12' : 'col-md-6'}">
            <label for="field_${index}" class="form-label">${field.label}${field.required ? ' *' : ''}</label>
            <input type="text" class="form-control" id="field_${index}" name="${field.label}"
                placeholder="${field.placeholder || ''}" ${field.required ? 'required' : ''}>
        </div>
    `;
}

function renderNumberField(field, index) {
    return `
        <div class="mb-3 ${field.fullWidth ? 'col-12' : 'col-md-6'}">
            <label for="field_${index}" class="form-label">${field.label}${field.required ? ' *' : ''}</label>
            <input type="number" class="form-control" id="field_${index}" name="${field.label}"
                placeholder="${field.placeholder || ''}" ${field.required ? 'required' : ''}>
        </div>
    `;
}

function renderEmailField(field, index) {
    return `
        <div class="mb-3 ${field.fullWidth ? 'col-12' : 'col-md-6'}">
            <label for="field_${index}" class="form-label">${field.label}${field.required ? ' *' : ''}</label>
            <input type="email" class="form-control" id="field_${index}" name="${field.label}"
                placeholder="${field.placeholder || ''}" ${field.required ? 'required' : ''}>
        </div>
    `;
}

function renderFileField(field, index) {
    return `
        <div class="mb-3 ${field.fullWidth ? 'col-12' : 'col-md-6'}">
            <label for="field_${index}" class="form-label">${field.label}${field.required ? ' *' : ''}</label>
            <input type="file" class="form-control" id="field_${index}" name="${field.label}"
                accept="${field.accept || ''}" ${field.required ? 'required' : ''}>
            <div class="form-text">Accepted file types: ${field.accept || 'All files'}</div>
        </div>
    `;
}

function renderTextareaField(field, index) {
    return `
        <div class="mb-3 ${field.fullWidth ? 'col-12' : 'col-md-6'}">
            <label for="field_${index}" class="form-label">${field.label}${field.required ? ' *' : ''}</label>
            <textarea class="form-control" id="field_${index}" name="${field.label}"
                placeholder="${field.placeholder || ''}" ${field.required ? 'required' : ''}></textarea>
        </div>
    `;
}

function renderSelectField(field, index) {
    const options = field.options ? field.options.map((option, i) =>
        `<option value="${option}">${option}</option>`
    ).join('') : '';

    return `
        <div class="mb-3 ${field.fullWidth ? 'col-12' : 'col-md-6'}">
            <label for="field_${index}" class="form-label">${field.label}${field.required ? ' *' : ''}</label>
            <select class="form-select" id="field_${index}" name="${field.label}" ${field.required ? 'required' : ''}>
                <option value="">Select an option</option>
                ${options}
            </select>
        </div>
    `;
}

function renderCheckboxField(field, index) {
    // If field has options, render multiple checkboxes
    if (field.options && field.options.length > 0) {
        const options = field.options.map((option, i) => `
            <div class="form-check">
                <input type="checkbox" class="form-check-input" id="field_${index}_${i}"
                    name="${field.label}[]" value="${option}">
                <label class="form-check-label" for="field_${index}_${i}">${option}</label>
            </div>
        `).join('');

        return `
            <div class="mb-3 ${field.fullWidth ? 'col-12' : 'col-md-6'}">
                <label class="form-label">${field.label}${field.required ? ' *' : ''}</label>
                ${options}
                ${field.required ? `<input type="hidden" name="${field.label}_required" value="1" class="checkbox-group-validator" data-group="${field.label}">` : ''}
            </div>
        `;
    } else {
        // Single checkbox
        return `
            <div class="mb-3 ${field.fullWidth ? 'col-12' : 'col-md-6'}">
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="field_${index}" name="${field.label}" value="1" ${field.required ? 'required' : ''}>
                    <label class="form-check-label" for="field_${index}">${field.checkboxLabel || field.label}</label>
                </div>
            </div>
        `;
    }
}

function renderRadioField(field, index) {
    const options = field.options ? field.options.map((option, i) => `
        <div class="form-check">
            <input type="radio" class="form-check-input" id="field_${index}_${i}"
                name="${field.label}" value="${option}" ${field.required ? 'required' : ''}>
            <label class="form-check-label" for="field_${index}_${i}">${option}</label>
        </div>
    `).join('') : '';

    return `
        <div class="mb-3 ${field.fullWidth ? 'col-12' : 'col-md-6'}">
            <label class="form-label">${field.label}${field.required ? ' *' : ''}</label>
            ${options}
        </div>
    `;
}

// Initialize conditional logic for form fields
function initializeConditionalLogic(fields) {
    // Add event listeners to all form inputs for conditional logic
    fields.forEach((field, index) => {
        const fieldElement = document.getElementById(`field_${index}`);
        if (fieldElement) {
            // Add change event listener
            fieldElement.addEventListener('change', function() {
                evaluateAllConditions(fields);
            });

            // Add input event listener for real-time updates
            fieldElement.addEventListener('input', function() {
                evaluateAllConditions(fields);
            });
        }
    });

    // Initial evaluation of all conditions
    evaluateAllConditions(fields);
}

// Evaluate all conditional logic
function evaluateAllConditions(fields) {
    fields.forEach((field, index) => {
        if (field.conditions && field.conditions.length > 0) {
            const shouldShow = evaluateFieldConditions(field.conditions, fields);
            const fieldContainer = document.getElementById(`field_${index}`).closest('.mb-3');

            if (fieldContainer) {
                if (shouldShow) {
                    fieldContainer.style.display = '';
                    // Re-enable required validation if field is required
                    if (field.required) {
                        document.getElementById(`field_${index}`).setAttribute('required', 'required');
                    }
                } else {
                    fieldContainer.style.display = 'none';
                    // Remove required validation when field is hidden
                    document.getElementById(`field_${index}`).removeAttribute('required');
                    // Clear the field value when hidden
                    const fieldElement = document.getElementById(`field_${index}`);
                    if (fieldElement.type === 'checkbox' || fieldElement.type === 'radio') {
                        fieldElement.checked = false;
                    } else {
                        fieldElement.value = '';
                    }
                }
            }
        }
    });
}

// Evaluate conditions for a specific field
function evaluateFieldConditions(conditions, fields) {
    // All conditions must be true (AND logic)
    return conditions.every(condition => {
        const triggerFieldIndex = condition.field;
        const triggerField = document.getElementById(`field_${triggerFieldIndex}`);

        if (!triggerField) {
            return false;
        }

        let fieldValue = '';
        if (triggerField.type === 'checkbox') {
            // For checkboxes, get all checked values
            const checkboxes = document.querySelectorAll(`input[name="${fields[triggerFieldIndex].label}"]:checked`);
            fieldValue = Array.from(checkboxes).map(cb => cb.value).join(',');
        } else if (triggerField.type === 'radio') {
            const radioButton = document.querySelector(`input[name="${fields[triggerFieldIndex].label}"]:checked`);
            fieldValue = radioButton ? radioButton.value : '';
        } else {
            fieldValue = triggerField.value;
        }

        return evaluateCondition(fieldValue, condition.operator, condition.value);
    });
}

// Evaluate a single condition
function evaluateCondition(fieldValue, operator, conditionValue) {
    switch (operator) {
        case 'equals':
            return fieldValue === conditionValue;
        case 'not_equals':
            return fieldValue !== conditionValue;
        case 'greater_than':
            return parseFloat(fieldValue) > parseFloat(conditionValue);
        case 'less_than':
            return parseFloat(fieldValue) < parseFloat(conditionValue);
        case 'contains':
            return fieldValue.toLowerCase().includes(conditionValue.toLowerCase());
        case 'not_contains':
            return !fieldValue.toLowerCase().includes(conditionValue.toLowerCase());
        case 'is_empty':
            return !fieldValue || fieldValue.trim() === '';
        case 'is_not_empty':
            return fieldValue && fieldValue.trim() !== '';
        default:
            return false;
    }
}

document.getElementById('formDisplay').addEventListener('submit', function(e) {
    e.preventDefault();

    const submitBtn = document.getElementById('submitBtn');
    const formAlert = document.getElementById('formAlert');

    // Disable submit button and show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Submitting...';

    // Show processing message
    formAlert.classList.remove('d-none', 'alert-danger');
    formAlert.classList.add('alert-info');
    formAlert.textContent = 'Processing your submission...';

    // Get the form data
    const formData = new FormData(this);

    // Use the globally stored form structure
    if (!globalFormStructure || globalFormStructure.length === 0) {
        formAlert.classList.remove('alert-info', 'alert-success');
        formAlert.classList.add('alert-danger', 'd-block');
        formAlert.textContent = 'Form structure not loaded. Please refresh the page and try again.';
        submitBtn.disabled = false;
        submitBtn.innerHTML = 'Submit';
        return;
    }

    console.log('Using global form structure for submission:', globalFormStructure);

    const hasFileFields = globalFormStructure.some(field => field.type === 'file');
    let submitPromise;

    if (hasFileFields) {
        // For forms with file uploads, we need to use FormData
        // Add form ID to the FormData
        formData.append('form_id', formId);

        // Ensure CSRF token is included (in case @csrf didn't work)
        if (!formData.has('_token')) {
            formData.append('_token', '{{ csrf_token() }}');
        }

        // Handle checkbox fields for FormData
        globalFormStructure.forEach(field => {
            if (field.type === 'checkbox') {
                if (field.options && field.options.length > 0) {
                    // This is a checkbox group - check if any values exist
                    const hasValues = Array.from(formData.keys()).some(key => key === field.label + '[]');
                    if (!hasValues) {
                        // Add empty array indicator
                        formData.append(field.label + '_empty', '1');
                    }
                } else {
                    // This is a single checkbox
                    if (!formData.has(field.label)) {
                        formData.append(field.label, '0');
                    }
                }
            }
        });

        // Submit the form with files
        submitPromise = fetch('{{ route("scientist.submit-form") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: formData
        });
    } else {
        // For forms without files, we can use JSON
        const data = {};

        // Process form data
        for (let [key, value] of formData.entries()) {
            // Handle checkbox arrays specially
            if (key.endsWith('[]')) {
                const baseKey = key.replace('[]', '');
                if (!data[baseKey]) {
                    data[baseKey] = [];
                }
                data[baseKey].push(value);
            } else if (value !== null && value !== '') {
                data[key] = value;
            }
        }

        // Handle checkbox fields
        globalFormStructure.forEach(field => {
            if (field.type === 'checkbox') {
                if (field.options && field.options.length > 0) {
                    // This is a checkbox group - if no data exists, set empty array
                    if (!data.hasOwnProperty(field.label)) {
                        data[field.label] = [];
                    }
                } else {
                    // This is a single checkbox - if no data exists, set to false/0
                    if (!data.hasOwnProperty(field.label)) {
                        data[field.label] = '0';
                    }
                }
            }
        });

        // Submit the form data as JSON
        submitPromise = fetch('{{ route("scientist.submit-form") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                _token: '{{ csrf_token() }}',
                form_id: formId,
                form_data: data
            })
        });
    }

    // Handle the response
    submitPromise
        .then(response => {
            if (!response.ok) {
                // Try to get error message from response
                return response.json().then(errorData => {
                    const errorMessage = errorData.error || errorData.message || 'Server returned an error';
                    throw new Error(errorMessage);
                }).catch(() => {
                    throw new Error('Server returned an error (status: ' + response.status + ')');
                });
            }
            return response.json();
        })
        .then(responseData => {
            // Re-enable submit button
            submitBtn.disabled = false;
            submitBtn.innerHTML = 'Submit';

            if (responseData.message) {
                // Show success message
                formAlert.classList.remove('alert-info', 'alert-danger');
                formAlert.classList.add('alert-success');
                formAlert.textContent = responseData.message;

                // Redirect after a short delay
                setTimeout(() => {
                    window.location.href = '/scientist/forms';
                }, 2000);
            }
        })
        .catch(error => {
            console.error('Error:', error);

            // Re-enable submit button
            submitBtn.disabled = false;
            submitBtn.innerHTML = 'Submit';

            // Show error message
            formAlert.classList.remove('alert-info', 'alert-success');
            formAlert.classList.add('alert-danger', 'd-block');

            // Try to get the actual error message from the server response
            if (error.message && error.message !== 'Server returned an error') {
                formAlert.textContent = 'Error: ' + error.message;
            } else {
                formAlert.textContent = 'An error occurred while submitting the form. Please try again.';
            }
        });
});

// Custom validation for checkbox groups
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('formDisplay');

    // Add custom validation for checkbox groups
    form.addEventListener('submit', function(e) {
        const checkboxValidators = document.querySelectorAll('.checkbox-group-validator');
        let isValid = true;

        checkboxValidators.forEach(validator => {
            const groupName = validator.dataset.group;
            const checkboxes = document.querySelectorAll(`input[name="${groupName}[]"]:checked`);

            if (checkboxes.length === 0) {
                // No checkboxes selected in required group
                isValid = false;

                // Add visual feedback
                const groupContainer = validator.closest('.mb-3');
                if (groupContainer) {
                    groupContainer.classList.add('has-error');

                    // Remove existing error message
                    const existingError = groupContainer.querySelector('.checkbox-error');
                    if (existingError) {
                        existingError.remove();
                    }

                    // Add error message
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'checkbox-error text-danger small mt-1';
                    errorDiv.textContent = 'Please select at least one option.';
                    groupContainer.appendChild(errorDiv);
                }
            } else {
                // Remove error styling if validation passes
                const groupContainer = validator.closest('.mb-3');
                if (groupContainer) {
                    groupContainer.classList.remove('has-error');
                    const existingError = groupContainer.querySelector('.checkbox-error');
                    if (existingError) {
                        existingError.remove();
                    }
                }
            }
        });

        if (!isValid) {
            e.preventDefault();
            e.stopPropagation();
        }
    });

    // Remove error styling when user selects checkboxes
    document.addEventListener('change', function(e) {
        if (e.target.type === 'checkbox' && e.target.name.endsWith('[]')) {
            const groupName = e.target.name.replace('[]', '');
            const validator = document.querySelector(`.checkbox-group-validator[data-group="${groupName}"]`);

            if (validator) {
                const checkboxes = document.querySelectorAll(`input[name="${groupName}[]"]:checked`);
                const groupContainer = validator.closest('.mb-3');

                if (checkboxes.length > 0 && groupContainer) {
                    groupContainer.classList.remove('has-error');
                    const existingError = groupContainer.querySelector('.checkbox-error');
                    if (existingError) {
                        existingError.remove();
                    }
                }
            }
        }
    });
});
</script>
@endpush
@endsection
