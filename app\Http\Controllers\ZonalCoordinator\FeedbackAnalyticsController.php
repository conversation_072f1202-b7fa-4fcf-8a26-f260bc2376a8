<?php

namespace App\Http\Controllers\ZonalCoordinator;

use App\Http\Controllers\Controller;
use App\Models\ActionPlan;
use App\Models\ParticipantFeedback;
use App\Models\ZonalCoordinator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class FeedbackAnalyticsController extends Controller
{
    /**
     * Display the feedback analytics dashboard.
     */
    public function index()
    {
        return view('zonal-coordinator.feedback-analytics.index');
    }

    /**
     * Get analytics data for all events in the coordinator's districts.
     */
    public function getAnalyticsData(Request $request)
    {
        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', Auth::id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'Zonal coordinator record not found'], 404);
            }

            // Get districts assigned to this coordinator
            $districtIds = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->pluck('district_id');

            // Get date range filter if provided
            $startDate = $request->input('start_date') ? Carbon::parse($request->input('start_date')) : Carbon::now()->subMonths(3);
            $endDate = $request->input('end_date') ? Carbon::parse($request->input('end_date')) : Carbon::now();

            // Get action plans in these districts
            $actionPlans = ActionPlan::whereIn('district_id', $districtIds)
                ->where('status', 'completed')
                ->whereBetween('planned_date', [$startDate, $endDate])
                ->with(['district:id,state,district'])
                ->get();

            $actionPlanIds = $actionPlans->pluck('id')->toArray();

            // Get feedback for these action plans
            $feedback = ParticipantFeedback::whereIn('action_plan_id', $actionPlanIds)
                ->with('actionPlan:id,title,planned_date,district_id')
                ->get();

            // Calculate overall statistics
            $stats = [
                'total_events' => count($actionPlanIds),
                'total_feedback' => $feedback->count(),
                'avg_feedback_per_event' => $feedback->count() > 0 ? round($feedback->count() / count($actionPlanIds), 1) : 0,
                'avg_benefit_rating' => $feedback->avg('benefit_rating') ? round($feedback->avg('benefit_rating'), 1) : 0,
                'avg_speaker_rating' => $feedback->avg('speaker_rating') ? round($feedback->avg('speaker_rating'), 1) : 0,
                'would_recommend_percentage' => $feedback->count() > 0
                    ? round(($feedback->where('would_recommend', true)->count() / $feedback->count()) * 100)
                    : 0,
                'by_source' => [
                    'web' => $feedback->where('source', 'web')->count(),
                    'excel' => $feedback->where('source', 'excel')->count(),
                    'mobile' => $feedback->where('source', 'mobile')->count(),
                ],
            ];

            // Calculate trend data (monthly)
            $trendData = $this->calculateTrendData($feedback, $startDate, $endDate);

            // Calculate district-wise statistics
            $districtStats = $this->calculateDistrictStats($feedback, $actionPlans);

            // Calculate topic analysis
            $topicAnalysis = $this->analyzeTopics($feedback);

            // Calculate rating distribution
            $ratingDistribution = [
                'benefit' => $this->calculateRatingDistribution($feedback, 'benefit_rating', 10),
                'speaker' => $this->calculateRatingDistribution($feedback, 'speaker_rating', 5),
            ];

            return response()->json([
                'stats' => $stats,
                'trend_data' => $trendData,
                'district_stats' => $districtStats,
                'topic_analysis' => $topicAnalysis,
                'rating_distribution' => $ratingDistribution,
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting analytics data: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get analytics data: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Calculate trend data for feedback over time.
     */
    private function calculateTrendData($feedback, $startDate, $endDate)
    {
        $months = [];
        $currentDate = clone $startDate;

        while ($currentDate <= $endDate) {
            $months[] = $currentDate->format('M Y');
            $currentDate->addMonth();
        }

        $benefitRatings = array_fill(0, count($months), 0);
        $speakerRatings = array_fill(0, count($months), 0);
        $recommendPercentages = array_fill(0, count($months), 0);
        $feedbackCounts = array_fill(0, count($months), 0);

        foreach ($feedback as $item) {
            $feedbackDate = Carbon::parse($item->created_at);
            $monthKey = $feedbackDate->format('M Y');

            if (($key = array_search($monthKey, $months)) !== false) {
                $feedbackCounts[$key]++;
                $benefitRatings[$key] += $item->benefit_rating;
                $speakerRatings[$key] += $item->speaker_rating;
                if ($item->would_recommend) {
                    $recommendPercentages[$key]++;
                }
            }
        }

        // Calculate averages
        for ($i = 0; $i < count($months); $i++) {
            if ($feedbackCounts[$i] > 0) {
                $benefitRatings[$i] = round($benefitRatings[$i] / $feedbackCounts[$i], 1);
                $speakerRatings[$i] = round($speakerRatings[$i] / $feedbackCounts[$i], 1);
                $recommendPercentages[$i] = round(($recommendPercentages[$i] / $feedbackCounts[$i]) * 100);
            }
        }

        return [
            'months' => $months,
            'benefit_ratings' => $benefitRatings,
            'speaker_ratings' => $speakerRatings,
            'recommend_percentages' => $recommendPercentages,
            'feedback_counts' => $feedbackCounts,
        ];
    }

    /**
     * Calculate district-wise statistics.
     */
    private function calculateDistrictStats($feedback, $actionPlans)
    {
        $districtStats = [];

        // Group action plans by district
        $actionPlansByDistrict = $actionPlans->groupBy('district_id');

        foreach ($actionPlansByDistrict as $districtId => $districtActionPlans) {
            $districtActionPlanIds = $districtActionPlans->pluck('id')->toArray();
            $districtFeedback = $feedback->whereIn('action_plan_id', $districtActionPlanIds);

            if ($districtFeedback->count() > 0) {
                $district = $districtActionPlans->first()->district;

                $districtStats[] = [
                    'district_id' => $districtId,
                    'district_name' => $district ? $district->district : 'Unknown',
                    'state_name' => $district ? $district->state : 'Unknown',
                    'event_count' => $districtActionPlans->count(),
                    'feedback_count' => $districtFeedback->count(),
                    'avg_benefit_rating' => round($districtFeedback->avg('benefit_rating'), 1),
                    'avg_speaker_rating' => round($districtFeedback->avg('speaker_rating'), 1),
                    'would_recommend_percentage' => round(($districtFeedback->where('would_recommend', true)->count() / $districtFeedback->count()) * 100),
                ];
            }
        }

        // Sort by feedback count (descending)
        usort($districtStats, function($a, $b) {
            return $b['feedback_count'] <=> $a['feedback_count'];
        });

        return $districtStats;
    }

    /**
     * Analyze most helpful topics from feedback.
     */
    private function analyzeTopics($feedback)
    {
        $topics = [];

        // Extract topics from feedback
        foreach ($feedback as $item) {
            if (!empty($item->most_helpful_topic)) {
                $topic = trim($item->most_helpful_topic);

                if (isset($topics[$topic])) {
                    $topics[$topic]++;
                } else {
                    $topics[$topic] = 1;
                }
            }
        }

        // Sort by count (descending)
        arsort($topics);

        // Take top 10 topics
        $topTopics = array_slice($topics, 0, 10, true);

        $result = [
            'labels' => array_keys($topTopics),
            'counts' => array_values($topTopics),
        ];

        return $result;
    }

    /**
     * Calculate rating distribution.
     */
    private function calculateRatingDistribution($feedback, $field, $maxRating)
    {
        $distribution = array_fill(1, $maxRating, 0);

        foreach ($feedback as $item) {
            $rating = $item->{$field};
            if ($rating >= 1 && $rating <= $maxRating) {
                $distribution[$rating]++;
            }
        }

        return [
            'labels' => range(1, $maxRating),
            'counts' => array_values($distribution),
        ];
    }

    /**
     * Get event-specific analytics data.
     */
    public function getEventAnalytics($actionPlanId)
    {
        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', Auth::id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'Zonal coordinator record not found'], 404);
            }

            // Get districts assigned to this coordinator
            $districtIds = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->pluck('district_id');

            // Verify the action plan is in one of the coordinator's districts
            $actionPlan = ActionPlan::where('id', $actionPlanId)
                ->whereIn('district_id', $districtIds)
                ->with(['district:id,state,district', 'scientist:id,name,email'])
                ->first();

            if (!$actionPlan) {
                return response()->json(['error' => 'Action plan not found or not in your districts'], 404);
            }

            // Get feedback for this action plan
            $feedback = ParticipantFeedback::where('action_plan_id', $actionPlanId)->get();

            // Calculate statistics
            $stats = [
                'total_feedback' => $feedback->count(),
                'avg_benefit_rating' => $feedback->avg('benefit_rating') ? round($feedback->avg('benefit_rating'), 1) : 0,
                'avg_speaker_rating' => $feedback->avg('speaker_rating') ? round($feedback->avg('speaker_rating'), 1) : 0,
                'would_recommend_percentage' => $feedback->count() > 0
                    ? round(($feedback->where('would_recommend', true)->count() / $feedback->count()) * 100)
                    : 0,
                'by_source' => [
                    'web' => $feedback->where('source', 'web')->count(),
                    'excel' => $feedback->where('source', 'excel')->count(),
                    'mobile' => $feedback->where('source', 'mobile')->count(),
                ],
            ];

            // Calculate rating distribution
            $ratingDistribution = [
                'benefit' => $this->calculateRatingDistribution($feedback, 'benefit_rating', 10),
                'speaker' => $this->calculateRatingDistribution($feedback, 'speaker_rating', 5),
            ];

            // Analyze topics
            $topicAnalysis = $this->analyzeTopics($feedback);

            // Get improvement suggestions
            $suggestions = $feedback->whereNotNull('improvement_suggestions')
                ->where('improvement_suggestions', '!=', '')
                ->pluck('improvement_suggestions')
                ->toArray();

            return response()->json([
                'action_plan' => $actionPlan,
                'stats' => $stats,
                'rating_distribution' => $ratingDistribution,
                'topic_analysis' => $topicAnalysis,
                'suggestions' => $suggestions,
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting action plan analytics: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get action plan analytics: ' . $e->getMessage()], 500);
        }
    }
}
