<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feedback QR Code - {{ $event->title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 20px;
            padding-bottom: 20px;
        }
        .qr-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            text-align: center;
        }
        .header {
            margin-bottom: 30px;
        }
        .header img {
            max-width: 200px;
            margin-bottom: 20px;
        }
        .qr-code {
            margin: 20px auto;
            max-width: 300px;
        }
        .qr-code img {
            width: 100%;
            height: auto;
        }
        .event-details {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: left;
        }
        .print-btn {
            margin-top: 20px;
        }
        @media print {
            .no-print {
                display: none;
            }
            .qr-container {
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="qr-container">
            <div class="header">
                <img src="{{ asset('images/logo.png') }}" alt="Mera Resham Mera Abhimaan" onerror="this.src='https://via.placeholder.com/200x80?text=Mera+Resham+Mera+Abhimaan'">
                <h2>Event Feedback QR Code</h2>
            </div>
            
            <div class="event-details">
                <h4>{{ $event->title }}</h4>
                <p><strong>Date:</strong> {{ \Carbon\Carbon::parse($event->start_date)->format('d M Y') }}</p>
                <p><strong>Location:</strong> {{ $event->location }}</p>
            </div>
            
            <p>Scan this QR code to provide feedback for the event:</p>
            
            <div class="qr-code">
                <img src="https://api.qrserver.com/v1/create-qr-code/?size=300x300&data={{ urlencode($feedbackUrl) }}" alt="Feedback QR Code">
            </div>
            
            <p class="text-muted">Or visit: <a href="{{ $feedbackUrl }}">{{ $feedbackUrl }}</a></p>
            
            <div class="no-print">
                <button class="btn btn-primary print-btn" onclick="window.print()">
                    <i class="fas fa-print"></i> Print QR Code
                </button>
                
                <a href="{{ url()->previous() }}" class="btn btn-secondary print-btn">
                    <i class="fas fa-arrow-left"></i> Back
                </a>
            </div>
        </div>
    </div>
</body>
</html>
