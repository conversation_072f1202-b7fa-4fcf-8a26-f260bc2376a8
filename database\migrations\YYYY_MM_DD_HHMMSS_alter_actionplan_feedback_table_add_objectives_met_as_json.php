<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('actionplan_feedback', function (Blueprint $table) {
            // Check if the column exists before attempting to modify it
            if (Schema::hasColumn('actionplan_feedback', 'objectives_met')) {
                // If it exists and is not already json, change it to json
                $table->json('objectives_met')->change();
            } else {
                // If it doesn't exist, add it as a json column
                $table->json('objectives_met')->nullable()->after('suggestions_for_improvement');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('actionplan_feedback', function (Blueprint $table) {
            // Revert the column type if needed, or drop it if it was newly added
            // This down method assumes you might want to revert to a text column
            // or drop it if it was added by this migration.
            // Adjust based on your specific needs for rollback.
            if (Schema::hasColumn('actionplan_feedback', 'objectives_met')) {
                $table->text('objectives_met')->change(); // Example: revert to text
            }
        });
    }
};
