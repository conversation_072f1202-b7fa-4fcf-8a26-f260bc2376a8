<?php

namespace App\Exports;

use App\Models\ActionPlanFeedback;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ActionPlanFeedbackExport implements FromCollection, WithHeadings, WithMapping
{
    protected $actionPlanId;

    public function __construct($actionPlanId)
    {
        $this->actionPlanId = $actionPlanId;
    }

    public function collection()
    {
        return ActionPlanFeedback::where('action_plan_id', $this->actionPlanId)
            ->with('actionPlan')
            ->get();
    }

    public function headings(): array
    {
        return [
            'Name',
            'Contact',
            'Benefit Rating',
            'Would Recommend',
            'Speaker Rating',
            'Most Helpful Topic',
            'Suggestions',
            'Date'
        ];
    }

    public function map($feedback): array
    {
        return [
            $feedback->name,
            $feedback->contact,
            $feedback->benefit_rating,
            $feedback->would_recommend ? 'Yes' : 'No',
            $feedback->speaker_rating,
            $feedback->most_helpful_topic,
            $feedback->suggestions,
            $feedback->created_at->format('Y-m-d H:i:s')
        ];
    }
}
