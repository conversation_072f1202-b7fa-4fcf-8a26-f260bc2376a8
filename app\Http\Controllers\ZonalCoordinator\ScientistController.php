<?php

namespace App\Http\Controllers\ZonalCoordinator;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\ZonalCoordinator;
use App\Models\StateData;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ScientistController extends Controller
{
    /**
     * Display the scientists page.
     */
    public function index()
    {
        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', Auth::id())->first();

            if (!$coordinator) {
                return redirect()->back()->with('error', 'Zonal coordinator record not found.');
            }

            // Get districts assigned to this coordinator
            $districtIds = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->pluck('district_id');

            // Get scientists assigned to these districts
            $scientistEmails = StateData::whereIn('id', $districtIds)
                ->whereNotNull('scientist')
                ->pluck('scientist')
                ->unique();

            $scientists = User::whereIn('email', $scientistEmails)
                ->where('role', 'scientist')
                ->select('id', 'name', 'email', 'phone_number', 'designation', 'created_at')
                ->get();

            // Get district assignments for each scientist
            foreach ($scientists as $scientist) {
                $districts = StateData::where('scientist', $scientist->email)
                    ->whereIn('id', $districtIds)
                    ->select('id', 'state', 'district', 'status')
                    ->get();

                $scientist->districts = $districts;
            }

            return view('zonal-coordinator.scientists.index', compact('scientists'));
        } catch (\Exception $e) {
            Log::error('Error in scientist index: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load scientists list.');
        }
    }

    /**
     * Get scientists in the coordinator's districts.
     */
    public function getScientists()
    {
        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', Auth::id())->first();

            if (!$coordinator) {
                Log::error('Zonal coordinator not found for user: ' . Auth::id());
                return response()->json(['error' => 'Zonal coordinator record not found'], 404);
            }

            Log::info('Found zonal coordinator', ['coordinator_id' => $coordinator->id]);

            // Get districts assigned to this coordinator
            $districtIds = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->pluck('district_id');

            Log::info('Found districts for coordinator', ['district_ids' => $districtIds->toArray()]);

            // Get detailed district information for debugging
            $districts = StateData::whereIn('id', $districtIds)->get();
            Log::info('District details', [
                'districts' => $districts->map(function($d) {
                    return [
                        'id' => $d->id,
                        'state' => $d->state,
                        'district' => $d->district,
                        'scientist' => $d->scientist,
                        'status' => $d->status
                    ];
                })->toArray()
            ]);

            // Get scientists assigned to these districts
            $scientistEmails = StateData::whereIn('id', $districtIds)
                ->whereNotNull('scientist')
                ->pluck('scientist')
                ->unique();

            Log::info('Found scientist emails', ['emails' => $scientistEmails->toArray()]);

            $scientists = User::whereIn('email', $scientistEmails)
                ->where('role', 'scientist')
                ->select('id', 'name', 'email', 'phone_number', 'designation', 'created_at')
                ->get();

            Log::info('Found scientists', ['count' => $scientists->count(), 'scientists' => $scientists->toArray()]);

            // Get district assignments for each scientist
            foreach ($scientists as $scientist) {
                $districts = StateData::where('scientist', $scientist->email)
                    ->whereIn('id', $districtIds)
                    ->select('id', 'state', 'district', 'status')
                    ->get();

                Log::info('Found districts for scientist', [
                    'scientist_email' => $scientist->email,
                    'districts' => $districts->toArray()
                ]);

                $scientist->districts = $districts;
            }

            // If no scientists found, provide helpful debugging info
            if ($scientists->isEmpty()) {
                $totalScientists = User::where('role', 'scientist')->count();
                $totalDistricts = StateData::count();
                $districtsWithScientists = StateData::whereNotNull('scientist')->count();

                Log::warning('No scientists found for zonal coordinator', [
                    'coordinator_id' => $coordinator->id,
                    'district_ids' => $districtIds->toArray(),
                    'scientist_emails' => $scientistEmails->toArray(),
                    'total_districts_in_system' => $totalDistricts,
                    'total_scientists_in_system' => $totalScientists,
                    'districts_with_scientists' => $districtsWithScientists
                ]);

                // Return debugging information for empty results
                return response()->json([
                    'scientists' => [],
                    'debug_info' => [
                        'coordinator_id' => $coordinator->id,
                        'assigned_district_ids' => $districtIds->toArray(),
                        'districts_details' => $districts->map(function($d) {
                            return [
                                'id' => $d->id,
                                'name' => $d->state . ' - ' . $d->district,
                                'scientist' => $d->scientist ?? 'NO SCIENTIST ASSIGNED',
                                'status' => $d->status
                            ];
                        })->toArray(),
                        'total_scientists_in_system' => $totalScientists,
                        'total_districts_in_system' => $totalDistricts,
                        'districts_with_scientists' => $districtsWithScientists,
                        'message' => 'No scientists are assigned to your districts. Please contact the administrator to assign scientists to districts: ' . $districtIds->implode(', ')
                    ]
                ]);
            }

            return response()->json($scientists);
        } catch (\Exception $e) {
            Log::error('Error getting scientists: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Failed to get scientists'], 500);
        }
    }

    /**
     * Get scientist details.
     */
    public function getScientistDetails($id)
    {
        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', Auth::id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'Zonal coordinator record not found'], 404);
            }

            // Get districts assigned to this coordinator
            $districtIds = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->pluck('district_id');

            // Get the scientist
            $scientist = User::where('id', $id)
                ->where('role', 'scientist')
                ->select('id', 'name', 'email', 'phone_number', 'designation', 'created_at')
                ->first();

            if (!$scientist) {
                return response()->json(['error' => 'Scientist not found'], 404);
            }

            // Get district assignments for this scientist
            $districts = StateData::where('scientist', $scientist->email)
                ->whereIn('id', $districtIds)
                ->select('id', 'state', 'district', 'status')
                ->get();

            $scientist->districts = $districts;

            // If the scientist is not assigned to any of the coordinator's districts, return an error
            if ($districts->isEmpty()) {
                return response()->json(['error' => 'Scientist not found in your districts'], 404);
            }

            return response()->json($scientist);
        } catch (\Exception $e) {
            Log::error('Error getting scientist details: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get scientist details'], 500);
        }
    }

    /**
     * Get scientist by email.
     */
    public function getScientistByEmail($email)
    {
        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', Auth::id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'Zonal coordinator record not found'], 404);
            }

            // Get districts assigned to this coordinator
            $districtIds = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->pluck('district_id');

            // Check if the scientist is assigned to any of the coordinator's districts
            $isAssigned = StateData::where('scientist', $email)
                ->whereIn('id', $districtIds)
                ->exists();

            if (!$isAssigned) {
                return response()->json(['error' => 'Scientist not found in your districts'], 404);
            }

            // Get the scientist
            $scientist = User::where('email', $email)
                ->where('role', 'scientist')
                ->select('id', 'name', 'email', 'phone_number', 'designation')
                ->first();

            if (!$scientist) {
                return response()->json(['error' => 'Scientist not found'], 404);
            }

            return response()->json($scientist);
        } catch (\Exception $e) {
            Log::error('Error getting scientist by email: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get scientist details'], 500);
        }
    }

    public function show($id)
    {
        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', Auth::id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'Zonal coordinator record not found.'], 404);
            }

            // Get districts assigned to this coordinator
            $districtIds = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->pluck('district_id');

            // Get the scientist
            $scientist = User::where('id', $id)
                ->where('role', 'scientist')
                ->select('id', 'name', 'email', 'phone_number', 'designation', 'created_at')
                ->first();

            if (!$scientist) {
                return response()->json(['error' => 'Scientist not found.'], 404);
            }

            // Get district assignments for this scientist
            $districts = StateData::where('scientist', $scientist->email)
                ->whereIn('id', $districtIds)
                ->select('id', 'state', 'district', 'status')
                ->get();

            $scientist->districts = $districts;

            // Get action plans for this scientist
            $actionPlans = DB::table('action_plans')
                ->where('scientist_id', $scientist->id)
                ->orderBy('planned_date', 'desc')
                ->get();

            // Calculate average rating for each action plan
            foreach ($actionPlans as $plan) {
                $ratings = [
                    $plan->zc_pvent_preparedness_feedback,
                    $plan->zc_communication_effectiveness_feedback,
                    $plan->zc_participant_engagement_feedback,
                    $plan->zc_timeline_adherence_feedback
                ];

                $validRatings = array_filter($ratings, function($rating) {
                    return $rating !== null && $rating !== '';
                });

                $plan->average_rating = !empty($validRatings) ? array_sum($validRatings) / count($validRatings) : 0;
            }

            $scientist->action_plans = $actionPlans;

            return response()->json($scientist);
        } catch (\Exception $e) {
            Log::error('Error fetching scientist details: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch scientist details.'], 500);
        }
    }

    /**
     * Show the assign scientist form.
     */
    public function showAssignForm()
    {
        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', Auth::id())->first();

            if (!$coordinator) {
                return redirect()->back()->with('error', 'Zonal coordinator record not found.');
            }

            // Get districts assigned to this coordinator
            $districtIds = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->pluck('district_id');

            $districts = StateData::whereIn('id', $districtIds)->get();
            $scientists = User::where('role', 'scientist')->get();

            return view('zonal-coordinator.scientists.assign', compact('districts', 'scientists'));
        } catch (\Exception $e) {
            Log::error('Error showing assign form: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load assign form.');
        }
    }

    /**
     * Assign a scientist to a district.
     */
    public function assignScientist(Request $request)
    {
        $request->validate([
            'district_id' => 'required|exists:state_data,id',
            'scientist_email' => 'required|email|exists:users,email',
        ]);

        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', Auth::id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'Zonal coordinator record not found'], 404);
            }

            // Check if the district belongs to this coordinator
            $districtIds = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->pluck('district_id');

            if (!$districtIds->contains($request->district_id)) {
                return response()->json(['error' => 'District not assigned to your zone'], 403);
            }

            // Check if the user is a scientist
            $scientist = User::where('email', $request->scientist_email)
                ->where('role', 'scientist')
                ->first();

            if (!$scientist) {
                return response()->json(['error' => 'Scientist not found'], 404);
            }

            // Assign the scientist to the district
            $district = StateData::find($request->district_id);
            $district->scientist = $request->scientist_email;
            $district->save();

            return response()->json([
                'message' => 'Scientist assigned successfully',
                'district' => $district->state . ' - ' . $district->district,
                'scientist' => $scientist->name
            ]);

        } catch (\Exception $e) {
            Log::error('Error assigning scientist: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to assign scientist'], 500);
        }
    }
}
