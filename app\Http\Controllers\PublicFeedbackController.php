<?php

namespace App\Http\Controllers;

use App\Models\ActionPlan;
use App\Models\UserFeedback;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
// Removed unused imports: Storage, QrCode, Event, ParticipantFeedback, ActionPlanFeedback

class PublicFeedbackController extends Controller
{
    // Removed event-related methods to avoid confusion
    // This controller now only handles action plan feedback

    /**
     * Show the feedback form for a specific action plan.
     */
    public function showActionPlanFeedbackForm($actionPlanId)
    {
        try {
            $actionPlan = ActionPlan::findOrFail($actionPlanId);

            // Allow feedback for action plans within 30 days (regardless of status) - for testing
            // TODO: Change back to 2 days in production
            $thirtyDaysAgo = now()->subDays(2);
            if ($actionPlan->planned_date < $thirtyDaysAgo) {
                return view('public.feedback.error', [
                    'message' => 'Feedback window has closed for this action plan.'
                ]);
            }

            return view('public.feedback.actionplan_form', compact('actionPlan'));
        } catch (\Exception $e) {
            Log::error('Error showing action plan feedback form: ' . $e->getMessage());
            return view('public.feedback.error', [
                'message' => 'Action plan not found or an error occurred.'
            ]);
        }
    }

    /**
     * Submit feedback for an action plan.
     */
    public function submitActionPlanFeedback(Request $request, $actionPlanId)
    {
        try {
            $actionPlan = ActionPlan::findOrFail($actionPlanId);

            // Check if feedback window is still open (regardless of status) - for testing
            // TODO: Change back to 2 days in production
            $thirtyDaysAgo = now()->subDays(2);
            if ($actionPlan->planned_date < $thirtyDaysAgo) {
                if ($request->wantsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Feedback window has closed for this action plan.'
                    ]);
                }
                return view('public.feedback.error', [
                    'message' => 'Feedback window has closed for this action plan.'
                ]);
            }

            // Validate the simplified feedback form
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'phone_number' => 'required|string|max:255',
                'benefit_rating' => 'required|integer|min:1|max:10',
                'would_recommend' => 'required|boolean',
                'most_helpful_topic' => 'nullable|string',
                'speaker_rating' => 'required|integer|min:1|max:5',
                'suggestions' => 'nullable|string', // We'll accept it but not save it to match table structure
            ]);

            if ($validator->fails()) {
                if ($request->wantsJson()) {
                    return response()->json([
                        'success' => false,
                        'errors' => $validator->errors()
                    ]);
                }
                return back()->withErrors($validator)->withInput();
            }

            // Create user feedback record
            $feedback = UserFeedback::create([
                'event_id' => $actionPlanId, // Using event_id to store action plan ID
                'name' => $request->name,
                'phone_number' => $request->phone_number,
                'benefit_rating' => $request->benefit_rating,
                'would_recommend' => $request->would_recommend,
                'most_helpful_topic' => $request->most_helpful_topic,
                'speaker_rating' => $request->speaker_rating,
            ]);

            // Log suggestions separately since they're not stored in the database
            if ($request->suggestions) {
                Log::info('User suggestions for action plan ' . $actionPlanId, [
                    'feedback_id' => $feedback->id,
                    'suggestions' => $request->suggestions
                ]);
            }

            Log::info('User feedback submitted successfully', [
                'feedback_id' => $feedback->id,
                'action_plan_id' => $actionPlanId,
                'benefit_rating' => $request->benefit_rating,
                'would_recommend' => $request->would_recommend,
                'speaker_rating' => $request->speaker_rating,
                'has_suggestions' => !empty($request->suggestions)
            ]);

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Thank you for your feedback!',
                    'feedback' => $feedback
                ]);
            }

            return view('public.feedback.success', [
                'actionPlan' => $actionPlan,
                'message' => 'Thank you for your valuable feedback! Your input helps us improve our programs.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error submitting action plan feedback: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'An error occurred while submitting your feedback. Please try again.'
                ]);
            }
            return view('public.feedback.error', [
                'message' => 'An error occurred while submitting your feedback. Please try again.'
            ]);
        }
    }

    /**
     * Show a QR code for the action plan feedback form.
     */
    public function showActionPlanQrCode($actionPlanId)
    {
        try {
            $actionPlan = ActionPlan::findOrFail($actionPlanId);
            $feedbackUrl = route('public.feedback.actionplan.form', ['actionPlanId' => $actionPlanId]);
            return view('public.feedback.qr', [
                'actionPlan' => $actionPlan,
                'feedbackUrl' => $feedbackUrl
            ]);
        } catch (\Exception $e) {
            Log::error('Error showing action plan QR code: ' . $e->getMessage());
            return view('public.feedback.error', [
                'message' => 'Action plan not found or an error occurred.'
            ]);
        }
    }
}
