<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        try {
            Schema::table('action_plans', function (Blueprint $table) {
                $table->text('cancellation_reason')->nullable()->after('status');
            });
            
            Log::info('Successfully added cancellation_reason column to action_plans table');
        } catch (\Exception $e) {
            Log::error('Error adding cancellation_reason column to action_plans table: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        try {
            Schema::table('action_plans', function (Blueprint $table) {
                $table->dropColumn('cancellation_reason');
            });
            
            Log::info('Successfully removed cancellation_reason column from action_plans table');
        } catch (\Exception $e) {
            Log::error('Error removing cancellation_reason column from action_plans table: ' . $e->getMessage());
            throw $e;
        }
    }
};
