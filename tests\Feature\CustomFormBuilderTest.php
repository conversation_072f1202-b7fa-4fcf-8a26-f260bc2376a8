<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\FormBuilder;
use App\Models\FormSubmission;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CustomFormBuilderTest extends TestCase
{
    use RefreshDatabase;

    protected $superAdmin;
    protected $scientist;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test users
        $this->superAdmin = User::factory()->create([
            'role' => 'super_admin',
            'email' => '<EMAIL>'
        ]);

        $this->scientist = User::factory()->create([
            'role' => 'scientist',
            'email' => '<EMAIL>'
        ]);
    }

    /** @test */
    public function super_admin_can_create_form_with_unique_name()
    {
        $formData = [
            'form_name' => 'Test Form',
            'target_user' => 'all_scientists',
            'form_structure' => json_encode([
                [
                    'type' => 'text',
                    'label' => 'Name',
                    'required' => true,
                    'placeholder' => 'Enter your name'
                ]
            ]),
            'last_date' => Carbon::now()->addDays(7)->format('Y-m-d')
        ];

        $response = $this->actingAs($this->superAdmin)
            ->postJson('/super-admin/form-builder/create', $formData);

        $response->assertStatus(200)
            ->assertJson(['message' => 'Form created successfully']);

        $this->assertDatabaseHas('form_builders', [
            'form_name' => 'Test Form',
            'target_user' => 'all_scientists'
        ]);
    }

    /** @test */
    public function super_admin_cannot_create_form_with_duplicate_name()
    {
        // Create first form
        FormBuilder::create([
            'form_name' => 'Duplicate Test',
            'target_user' => 'all_scientists',
            'form_structure' => json_encode([['type' => 'text', 'label' => 'Test']])
        ]);

        // Try to create second form with same name
        $formData = [
            'form_name' => 'Duplicate Test',
            'target_user' => 'pre_cocoon',
            'form_structure' => json_encode([['type' => 'text', 'label' => 'Test2']]),
        ];

        $response = $this->actingAs($this->superAdmin)
            ->postJson('/super-admin/form-builder/create', $formData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['form_name']);
    }

    /** @test */
    public function scientist_can_submit_form_before_deadline()
    {
        $form = FormBuilder::create([
            'form_name' => 'Deadline Test Form',
            'target_user' => 'all_scientists',
            'form_structure' => json_encode([
                [
                    'type' => 'text',
                    'label' => 'Name',
                    'required' => true
                ]
            ]),
            'last_date' => Carbon::now()->addDays(7)
        ]);

        $submissionData = [
            'form_id' => $form->id,
            'form_data' => json_encode(['Name' => 'Test Scientist'])
        ];

        $response = $this->actingAs($this->scientist)
            ->postJson('/submit-form', $submissionData);

        $response->assertStatus(200)
            ->assertJson(['message' => 'Form submitted successfully']);
    }

    /** @test */
    public function scientist_cannot_submit_form_after_deadline()
    {
        $form = FormBuilder::create([
            'form_name' => 'Expired Form',
            'target_user' => 'all_scientists',
            'form_structure' => json_encode([
                [
                    'type' => 'text',
                    'label' => 'Name',
                    'required' => true
                ]
            ]),
            'last_date' => Carbon::now()->subDays(1) // Past deadline
        ]);

        $submissionData = [
            'form_id' => $form->id,
            'form_data' => json_encode(['Name' => 'Test Scientist'])
        ];

        $response = $this->actingAs($this->scientist)
            ->postJson('/submit-form', $submissionData);

        $response->assertStatus(403)
            ->assertJsonFragment(['error' => 'This form is no longer accepting submissions']);
    }

    /** @test */
    public function scientist_can_resubmit_form_after_admin_updates()
    {
        // Create form and submit
        $form = FormBuilder::create([
            'form_name' => 'Resubmit Test Form',
            'target_user' => 'all_scientists',
            'form_structure' => json_encode([
                [
                    'type' => 'text',
                    'label' => 'Name',
                    'required' => true
                ]
            ])
        ]);

        // First submission
        $submissionData = [
            'form_id' => $form->id,
            'form_data' => json_encode(['Name' => 'First Submission'])
        ];

        $this->actingAs($this->scientist)
            ->postJson('/submit-form', $submissionData);

        // Admin updates the form
        $updateData = [
            'form_name' => 'Resubmit Test Form',
            'target_user' => 'all_scientists',
            'form_structure' => json_encode([
                [
                    'type' => 'text',
                    'label' => 'Full Name',
                    'required' => true
                ],
                [
                    'type' => 'email',
                    'label' => 'Email',
                    'required' => true
                ]
            ])
        ];

        $this->actingAs($this->superAdmin)
            ->putJson("/super-admin/form-builder/update/{$form->id}", $updateData);

        // Scientist should be able to resubmit
        $newSubmissionData = [
            'form_id' => $form->id,
            'form_data' => json_encode([
                'Full Name' => 'Updated Submission',
                'Email' => '<EMAIL>'
            ])
        ];

        $response = $this->actingAs($this->scientist)
            ->postJson('/submit-form', $newSubmissionData);

        $response->assertStatus(200)
            ->assertJson(['message' => 'Form submitted successfully']);
    }

    /** @test */
    public function scientist_cannot_submit_duplicate_form_without_admin_update()
    {
        $form = FormBuilder::create([
            'form_name' => 'No Duplicate Form',
            'target_user' => 'all_scientists',
            'form_structure' => json_encode([
                [
                    'type' => 'text',
                    'label' => 'Name',
                    'required' => true
                ]
            ])
        ]);

        // First submission
        $submissionData = [
            'form_id' => $form->id,
            'form_data' => json_encode(['Name' => 'First Submission'])
        ];

        $this->actingAs($this->scientist)
            ->postJson('/submit-form', $submissionData);

        // Try to submit again without admin update
        $response = $this->actingAs($this->scientist)
            ->postJson('/submit-form', $submissionData);

        $response->assertStatus(400)
            ->assertJsonFragment(['error' => 'You have already submitted this form']);
    }
}
