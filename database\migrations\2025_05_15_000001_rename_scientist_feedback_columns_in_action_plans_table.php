<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        try {
            // Add the new columns
            Schema::table('action_plans', function (Blueprint $table) {
                $table->integer('scientist_event_success_rating')->nullable();
                $table->text('scientist_challenges_faced')->nullable();
                $table->text('scientist_suggestions_for_improvement')->nullable();
                $table->json('scientist_objectives_met')->nullable();
                $table->text('scientist_self_assessed_learning_outcome')->nullable();
            });

            Log::info('Successfully added scientist feedback columns to action_plans table');
        } catch (\Exception $e) {
            Log::error('Error adding scientist feedback columns: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        try {
            Schema::table('action_plans', function (Blueprint $table) {
                $table->dropColumn([
                    'scientist_event_success_rating',
                    'scientist_challenges_faced',
                    'scientist_suggestions_for_improvement',
                    'scientist_objectives_met',
                    'scientist_self_assessed_learning_outcome',
                ]);
            });

            Log::info('Successfully removed scientist feedback columns from action_plans table');
        } catch (\Exception $e) {
            Log::error('Error removing scientist feedback columns: ' . $e->getMessage());
            throw $e;
        }
    }
};
