<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('actionplan_feedback', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('action_plan_id')->constrained()->onDelete('cascade');
            $table->integer('event_success_rating')->nullable();
            $table->text('challenges_faced')->nullable();
            $table->text('suggestions_for_improvement')->nullable();
            $table->json('objectives_met')->nullable();
            $table->text('self_assessed_learning_outcome')->nullable();
            $table->integer('male_participants')->default(0);
            $table->integer('female_participants')->default(0);
            $table->integer('transgender_participants')->default(0);
            $table->integer('st_participants')->default(0);
            $table->integer('sc_participants')->default(0);
            $table->integer('general_participants')->default(0);
            $table->integer('obc_participants')->default(0);
            $table->string('photo1')->nullable();
            $table->string('photo2')->nullable();
            $table->string('photo3')->nullable();
            $table->string('photo4')->nullable();
            $table->string('gis_location')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('actionplan_feedback');
    }
};
