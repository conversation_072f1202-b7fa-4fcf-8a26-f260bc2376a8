<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('actionplan_feedback', function (Blueprint $table) {
            $table->string('name')->nullable();
            $table->string('contact')->nullable();
            $table->integer('benefit_rating')->nullable();
            $table->integer('speaker_rating')->nullable();
            $table->boolean('would_recommend')->nullable();
            $table->string('most_helpful_topic')->nullable();
            $table->text('suggestions')->nullable();

            // Add unique constraint for contact and action_plan_id
            $table->unique(['contact', 'action_plan_id']);
        });
    }

    public function down()
    {
        Schema::table('actionplan_feedback', function (Blueprint $table) {
            $table->dropColumn([
                'name',
                'contact',
                'benefit_rating',
                'speaker_rating',
                'would_recommend',
                'most_helpful_topic',
                'suggestions'
            ]);
            $table->dropUnique(['contact', 'action_plan_id']);
        });
    }
};
