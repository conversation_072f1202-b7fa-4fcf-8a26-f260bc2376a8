# Form Submission Troubleshooting Guide

## Common Error: "An error occurred while submitting the form. Please try again."

This generic error message can have several underlying causes. Here's how to diagnose and fix them:

## 🔍 **Quick Diagnosis Steps**

### 1. Check Browser Console
1. Open your browser's Developer Tools (F12)
2. Go to the Console tab
3. Try submitting the form again
4. Look for specific error messages

### 2. Check Network Tab
1. Open Developer Tools → Network tab
2. Submit the form
3. Look for the form submission request
4. Check the response status and error details

## 🚨 **Common Issues and Solutions**

### **Issue 1: 419 Page Expired (CSRF Token Error)**
**Symptoms:** HTTP 419 error, "Page Expired" message
**Causes:** 
- Session expired
- Invalid CSRF token
- Browser cache issues

**Solutions:**
1. **Refresh the page** and try again
2. **Clear browser cache** and cookies
3. **Log out and log back in**
4. Check if you've been idle for too long

### **Issue 2: 404 Not Found (Route Error)**
**Symptoms:** HTTP 404 error, "Server returned an error (status: 404)"
**Causes:**
- Incorrect form submission URL
- Route configuration issues
- Server routing problems

**Solutions:**
1. **Refresh the page** and try again
2. **Clear browser cache** completely
3. **Contact administrator** - this indicates a technical configuration issue

### **Issue 3: 403 Forbidden (Authentication/Authorization)**
**Symptoms:** HTTP 403 error, access denied
**Causes:**
- Not logged in
- Wrong user role
- Form access restrictions

**Solutions:**
1. **Ensure you're logged in** as a scientist
2. **Check form permissions** - some forms are restricted to specific user types
3. **Contact administrator** if you should have access

### **Issue 4: 500 Internal Server Error**
**Symptoms:** HTTP 500 error, "Server returned an error (status: 500)"
**Causes:**
- Server-side code errors
- Database connection issues
- Form data format problems
- Missing form tables

**Solutions:**
1. **Refresh the page** and try again
2. **Clear browser cache** completely
3. **Contact administrator** - this indicates a technical server issue

### **Issue 5: Form Configuration Errors**
**Symptoms:** "Form configuration error" messages
**Causes:**
- Missing database tables
- Form structure mismatch
- Database connectivity issues

**Solutions:**
1. **Contact administrator** - this requires backend fixes
2. **Administrator can run:** `php artisan forms:fix-tables`

### **Issue 6: Form Deadline Expired**
**Symptoms:** "This form is no longer accepting submissions"
**Causes:**
- Form submission deadline has passed

**Solutions:**
1. **Contact administrator** to extend the deadline
2. **Check form details** for the actual deadline

### **Issue 7: Duplicate Submission**
**Symptoms:** "You have already submitted this form"
**Causes:**
- Form only allows one submission per user
- Previous submission exists

**Solutions:**
1. **Check if resubmission is allowed** after admin updates
2. **Contact administrator** if you need to update your submission

## 🛠️ **For Administrators**

### **Diagnostic Commands**
```bash
# Check form tables
php artisan forms:fix-tables --dry-run

# Create missing tables
php artisan forms:fix-tables

# Check application logs
tail -f storage/logs/laravel.log
```

### **Common Admin Fixes**

1. **Create Missing Form Tables:**
   ```bash
   php artisan forms:fix-tables
   ```

2. **Check Form Configuration:**
   ```bash
   php artisan tinker
   >>> App\Models\FormBuilder::all(['id', 'form_name', 'target_user', 'last_date'])
   ```

3. **Verify User Permissions:**
   ```bash
   php artisan tinker
   >>> App\Models\User::where('email', '<EMAIL>')->first()->role
   ```

4. **Check Recent Submissions:**
   ```bash
   php artisan tinker
   >>> App\Models\FormSubmission::latest()->take(5)->get()
   ```

## 📋 **Prevention Tips**

### **For Users:**
1. **Don't keep forms open for too long** - sessions expire
2. **Save your work frequently** if the form is long
3. **Use a stable internet connection**
4. **Don't use browser back button** during submission

### **For Administrators:**
1. **Run regular health checks** on form tables
2. **Monitor application logs** for errors
3. **Set appropriate session timeouts**
4. **Test forms after creating them**

## 🆘 **When to Contact Support**

Contact your system administrator if you see:
- "Form configuration error" messages
- Persistent 500 server errors
- Database connection errors
- Issues affecting multiple users

## 📞 **Getting Help**

When reporting issues, please include:
1. **Exact error message** from browser console
2. **Form name and ID** you're trying to submit
3. **Your user role** (scientist, admin, etc.)
4. **Steps to reproduce** the issue
5. **Browser and version** you're using

---

*Last updated: June 2025*
