@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Feedback for {{ $actionPlan->title }}</h5>
            <div>
                <a href="{{ route('feedback.template.download') }}" class="btn btn-secondary">Download Template</a>
                <a href="{{ route('feedback.export', $actionPlan->id) }}" class="btn btn-primary">Export Feedback</a>
                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#importModal">Import Feedback</button>
            </div>
        </div>

        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Contact</th>
                            <th>Benefit Rating</th>
                            <th>Would Recommend</th>
                            <th>Speaker Rating</th>
                            <th>Most Helpful Topic</th>
                            <th>Source</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($feedback as $item)
                        <tr>
                            <td>{{ $item->name }}</td>
                            <td>{{ $item->contact }}</td>
                            <td>{{ $item->benefit_rating }}/5</td>
                            <td>{{ $item->would_recommend ? 'Yes' : 'No' }}</td>
                            <td>{{ $item->speaker_rating }}/5</td>
                            <td>{{ $item->most_helpful_topic }}</td>
                            <td>{{ $item->source ?? 'QR Code' }}</td>
                            <td>{{ $item->created_at->format('Y-m-d H:i') }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import Feedback</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('feedback.import', $actionPlan->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="file" class="form-label">Excel File</label>
                        <input type="file" class="form-control" id="file" name="file" accept=".xlsx,.xls" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Import</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
