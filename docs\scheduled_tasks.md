# Scheduled Tasks Documentation

This document provides information about the scheduled tasks implemented for the Mera Resham Mera Abhimaan feedback system.

## Overview

The system includes several scheduled tasks that automatically send notifications and reports to zonal coordinators based on feedback data. These tasks are implemented using <PERSON><PERSON>'s task scheduler and can be configured to run at different intervals.

## Available Commands

### 1. Send Feedback Summaries

**Command:** `feedback:send-summaries`

**Description:** Sends feedback summary reports to zonal coordinators.

**Options:**
- `--type=daily|weekly`: The type of summary to send (default: daily)

**Schedule:**
- Daily summaries: Every day at 8:00 AM
- Weekly summaries: Every Monday at 9:00 AM

**Example:**
```bash
php artisan feedback:send-summaries --type=daily
```

### 2. Send Low Rating Alerts

**Command:** `feedback:send-low-rating-alerts`

**Description:** Checks for low ratings in recent feedback and sends alerts to zonal coordinators.

**Options:**
- `--days=N`: Number of days to look back for low ratings (default: 1)

**Schedule:** Every 6 hours

**Example:**
```bash
php artisan feedback:send-low-rating-alerts --days=1
```

### 3. Send New Feedback Notifications

**Command:** `feedback:send-new-notifications`

**Description:** Sends notifications about new feedback submissions to zonal coordinators.

**Options:**
- `--minutes=N`: Number of minutes to look back for new feedback (default: 60)

**Schedule:** Every hour

**Example:**
```bash
php artisan feedback:send-new-notifications --minutes=60
```

### 4. Test Notification System

**Command:** `feedback:test-notifications`

**Description:** Tests the notification system by sending test emails.

**Arguments:**
- `email`: The email address to send test notifications to
- `type`: The type of notification to test (all, summary, low-rating, new-feedback) (default: all)

**Example:**
```bash
php artisan feedback:test-notifications <EMAIL> summary
```

## Setting Up the Scheduler

To enable the scheduled tasks, you need to add the following Cron entry to your server:

```
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

This Cron entry will run the Laravel scheduler every minute, which will then execute the scheduled tasks at their specified times.

## Configuration

### Email Configuration

The notification system uses Laravel's mail configuration. Make sure your `.env` file includes the following settings:

```
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Mera Resham Mera Abhimaan"
```

### Notification Settings

Zonal coordinators can configure their notification preferences in the notification settings page. The following settings are available:

- **New Feedback Notifications:** Receive notifications when new feedback is submitted
- **Daily Summary Reports:** Receive daily summary reports of feedback received
- **Weekly Summary Reports:** Receive weekly summary reports of feedback received
- **Low Rating Alerts:** Receive alerts when feedback ratings fall below the threshold
- **Low Rating Threshold:** Ratings below this threshold will trigger low rating alerts

## Logs

The output of each scheduled task is appended to a log file in the `storage/logs` directory:

- Daily summaries: `feedback-daily-summaries.log`
- Weekly summaries: `feedback-weekly-summaries.log`
- Low rating alerts: `feedback-low-rating-alerts.log`
- New feedback notifications: `feedback-new-notifications.log`

You can check these logs to troubleshoot any issues with the scheduled tasks.

## Manual Execution

You can manually execute any of the commands using the Laravel Artisan CLI. For example:

```bash
php artisan feedback:send-summaries --type=daily
```

This is useful for testing or for running the tasks outside of their scheduled times.
