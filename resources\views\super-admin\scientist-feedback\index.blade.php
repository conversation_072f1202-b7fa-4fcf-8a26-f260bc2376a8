@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Scientist Feedback') }}</h5>
                    <div>
                        <button id="addFeedbackBtn" class="btn btn-sm btn-success me-2">Add New Feedback</button>
                        <a href="{{ route('super-admin.dashboard') }}" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>Record and manage feedback for scientists based on their performance.</li>
                            <li>Feedback can be linked to specific events or provided as general performance assessment.</li>
                            <li>Rate scientists on a scale of 1-5 for various performance metrics.</li>
                        </ul>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Scientist</th>
                                    <th>Event</th>
                                    <th>Overall Rating</th>
                                    <th>Detailed Ratings</th>
                                    <th>Submitted By</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="feedbackTableBody">
                                <!-- Feedback entries will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Feedback Form Modal -->
                    <div class="modal fade" id="feedbackModal" tabindex="-1" aria-labelledby="feedbackModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="feedbackModalLabel">Add New Feedback</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <form id="feedbackForm">
                                        <input type="hidden" id="feedbackId" name="id">

                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="form-group mb-3">
                                                    <label for="scientist_id">Scientist:</label>
                                                    <select class="form-control" id="scientist_id" name="scientist_id" required>
                                                        <option value="">-- Select Scientist --</option>
                                                        <!-- Scientists will be loaded here -->
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <h5 class="mt-4 mb-3">Performance Ratings (1-5 scale)</h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="overall_rating">Overall Rating:</label>
                                                    <select class="form-control" id="overall_rating" name="overall_rating" required>
                                                        <option value="">-- Select Rating --</option>
                                                        <option value="1">1 - Poor</option>
                                                        <option value="2">2 - Below Average</option>
                                                        <option value="3">3 - Average</option>
                                                        <option value="4">4 - Good</option>
                                                        <option value="5">5 - Excellent</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="preparedness_rating">Event Preparedness:</label>
                                                    <select class="form-control" id="preparedness_rating" name="preparedness_rating" required>
                                                        <option value="">-- Select Rating --</option>
                                                        <option value="1">1 - Poor</option>
                                                        <option value="2">2 - Below Average</option>
                                                        <option value="3">3 - Average</option>
                                                        <option value="4">4 - Good</option>
                                                        <option value="5">5 - Excellent</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-group mb-3">
                                                    <label for="communication_rating">Communication Effectiveness:</label>
                                                    <select class="form-control" id="communication_rating" name="communication_rating" required>
                                                        <option value="">-- Select Rating --</option>
                                                        <option value="1">1 - Poor</option>
                                                        <option value="2">2 - Below Average</option>
                                                        <option value="3">3 - Average</option>
                                                        <option value="4">4 - Good</option>
                                                        <option value="5">5 - Excellent</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group mb-3">
                                                    <label for="engagement_rating">Participant Engagement:</label>
                                                    <select class="form-control" id="engagement_rating" name="engagement_rating" required>
                                                        <option value="">-- Select Rating --</option>
                                                        <option value="1">1 - Poor</option>
                                                        <option value="2">2 - Below Average</option>
                                                        <option value="3">3 - Average</option>
                                                        <option value="4">4 - Good</option>
                                                        <option value="5">5 - Excellent</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group mb-3">
                                                    <label for="adherence_rating">Timeline & Objective Adherence:</label>
                                                    <select class="form-control" id="adherence_rating" name="adherence_rating" required>
                                                        <option value="">-- Select Rating --</option>
                                                        <option value="1">1 - Poor</option>
                                                        <option value="2">2 - Below Average</option>
                                                        <option value="3">3 - Average</option>
                                                        <option value="4">4 - Good</option>
                                                        <option value="5">5 - Excellent</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group mb-3 mt-3">
                                            <label for="remarks">Remarks & Notes:</label>
                                            <textarea class="form-control" id="remarks" name="remarks" rows="4"></textarea>
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                    <button type="button" class="btn btn-primary" id="saveFeedbackBtn">Save Feedback</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Delete Confirmation Modal -->
                    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="deleteConfirmModalLabel">Confirm Delete</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <p>Are you sure you want to delete this feedback entry? This action cannot be undone.</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete Feedback</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const feedbackTableBody = document.getElementById('feedbackTableBody');
        const addFeedbackBtn = document.getElementById('addFeedbackBtn');
        const feedbackForm = document.getElementById('feedbackForm');
        const saveFeedbackBtn = document.getElementById('saveFeedbackBtn');
        const feedbackModal = new bootstrap.Modal(document.getElementById('feedbackModal'));
        const deleteConfirmModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
        const scientistSelect = document.getElementById('scientist_id');
        let currentFeedbackId = null;

        // Load feedback entries
        loadFeedback();

        // Load scientists
        loadScientists();

        // Add feedback button click
        addFeedbackBtn.addEventListener('click', function() {
            resetForm();
            document.getElementById('feedbackModalLabel').textContent = 'Add New Feedback';
            feedbackModal.show();
        });

        // Save feedback button click
        saveFeedbackBtn.addEventListener('click', function() {
            if (!feedbackForm.checkValidity()) {
                feedbackForm.reportValidity();
                return;
            }

            const formData = new FormData(feedbackForm);
            const data = {};
            
            formData.forEach((value, key) => {
                data[key] = value;
            });

            fetch('{{ route("super-admin.scientist-feedback.save") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    alert(data.message);
                    feedbackModal.hide();
                    loadFeedback();
                } else if (data.error) {
                    alert(data.error);
                }
            })
            .catch(error => {
                console.error('Error saving feedback:', error);
                alert('An error occurred while saving the feedback information.');
            });
        });

        // Confirm delete button click
        confirmDeleteBtn.addEventListener('click', function() {
            if (currentFeedbackId) {
                fetch(`{{ url("super-admin/scientist-feedback/delete") }}/${currentFeedbackId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.message) {
                        alert(data.message);
                        deleteConfirmModal.hide();
                        loadFeedback();
                    } else if (data.error) {
                        alert(data.error);
                    }
                })
                .catch(error => {
                    console.error('Error deleting feedback:', error);
                    alert('An error occurred while deleting the feedback entry.');
                });
            }
        });

        // Load feedback function
        function loadFeedback() {
            fetch('{{ route("super-admin.scientist-feedback.all") }}')
                .then(response => response.json())
                .then(data => {
                    feedbackTableBody.innerHTML = '';
                    
                    data.forEach(feedback => {
                        const row = document.createElement('tr');
                        
                        // Format date
                        const createdDate = new Date(feedback.created_at);
                        const formattedDate = createdDate.toLocaleDateString();
                        
                        // Format ratings
                        const detailedRatings = `
                            <div>Preparedness: ${getRatingStars(feedback.preparedness_rating)}</div>
                            <div>Communication: ${getRatingStars(feedback.communication_rating)}</div>
                            <div>Engagement: ${getRatingStars(feedback.engagement_rating)}</div>
                            <div>Adherence: ${getRatingStars(feedback.adherence_rating)}</div>
                        `;
                        
                        row.innerHTML = `
                            <td>${feedback.scientist ? feedback.scientist.name : 'N/A'}</td>
                            <td>General Feedback</td>
                            <td>${getRatingStars(feedback.overall_rating)}</td>
                            <td>${detailedRatings}</td>
                            <td>${feedback.creator ? feedback.creator.name : 'System'}</td>
                            <td>${formattedDate}</td>
                            <td>
                                <button class="btn btn-sm btn-primary edit-btn" data-id="${feedback.id}">Edit</button>
                                <button class="btn btn-sm btn-danger delete-btn" data-id="${feedback.id}">Delete</button>
                            </td>
                        `;
                        
                        feedbackTableBody.appendChild(row);
                    });
                    
                    // Add event listeners to edit buttons
                    document.querySelectorAll('.edit-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            const feedbackId = this.dataset.id;
                            loadFeedbackDetails(feedbackId);
                        });
                    });
                    
                    // Add event listeners to delete buttons
                    document.querySelectorAll('.delete-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            currentFeedbackId = this.dataset.id;
                            deleteConfirmModal.show();
                        });
                    });
                })
                .catch(error => console.error('Error loading feedback:', error));
        }

        // Load scientists function
        function loadScientists() {
            fetch('{{ route("super-admin.scientist-feedback.scientists") }}')
                .then(response => response.json())
                .then(data => {
                    scientistSelect.innerHTML = '<option value="">-- Select Scientist --</option>';
                    
                    data.forEach(scientist => {
                        const option = document.createElement('option');
                        option.value = scientist.id;
                        option.textContent = `${scientist.name} (${scientist.email})`;
                        scientistSelect.appendChild(option);
                    });
                })
                .catch(error => console.error('Error loading scientists:', error));
        }



        // Load feedback details function
        function loadFeedbackDetails(id) {
            fetch(`{{ url("super-admin/scientist-feedback/details") }}/${id}`)
                .then(response => response.json())
                .then(feedback => {
                    resetForm();
                    
                    document.getElementById('feedbackId').value = feedback.id;
                    document.getElementById('scientist_id').value = feedback.scientist_id;
                    document.getElementById('overall_rating').value = feedback.overall_rating;
                    document.getElementById('preparedness_rating').value = feedback.preparedness_rating;
                    document.getElementById('communication_rating').value = feedback.communication_rating;
                    document.getElementById('engagement_rating').value = feedback.engagement_rating;
                    document.getElementById('adherence_rating').value = feedback.adherence_rating;
                    document.getElementById('remarks').value = feedback.remarks || '';
                    document.getElementById('feedbackModalLabel').textContent = 'Edit Feedback';
                    
                    feedbackModal.show();
                })
                .catch(error => console.error('Error loading feedback details:', error));
        }

        // Get rating stars function
        function getRatingStars(rating) {
            // Handle null, undefined, or invalid ratings
            if (!rating || rating === null || rating === undefined || isNaN(rating)) {
                return '<span class="text-muted">No rating</span>';
            }

            const ratingValue = parseInt(rating);
            if (ratingValue < 1 || ratingValue > 5) {
                return '<span class="text-muted">Invalid rating</span>';
            }

            const fullStar = '<i class="fas fa-star text-warning"></i>';
            const emptyStar = '<i class="far fa-star text-muted"></i>';
            let stars = '';

            for (let i = 1; i <= 5; i++) {
                stars += i <= ratingValue ? fullStar : emptyStar;
            }

            return stars + ` (${ratingValue}/5)`;
        }

        // Reset form function
        function resetForm() {
            feedbackForm.reset();
            document.getElementById('feedbackId').value = '';
        }
    });
</script>
@endpush
