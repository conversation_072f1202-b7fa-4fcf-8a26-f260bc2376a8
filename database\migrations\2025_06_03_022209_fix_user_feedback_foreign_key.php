<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_feedback', function (Blueprint $table) {
            // Drop the existing foreign key constraint that points to events table
            $table->dropForeign('user_feedback_ibfk_1');

            // Add the correct foreign key constraint that points to action_plans table
            $table->foreign('event_id')->references('id')->on('action_plans')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_feedback', function (Blueprint $table) {
            // Drop the action_plans foreign key
            $table->dropForeign(['event_id']);

            // Restore the original events foreign key (if needed)
            // $table->foreign('event_id')->references('id')->on('events')->onDelete('cascade');
        });
    }
};
