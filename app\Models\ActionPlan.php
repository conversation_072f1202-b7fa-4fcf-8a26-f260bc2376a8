<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ActionPlan extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'scientist_id',
        'district_id',
        'type',
        'title',
        'location',
        'planned_date',
        'expected_participants',
        'required_coordinators',
        'status',
        'cancellation_reason',
        'scientist_feedback_status',
        'zc_feedback_status',
        'dsc_feedback_status',
        'super_admin_feedback_status',
        'male_participants',
        'female_participants',
        'transgender_participants',
        'st_participants',
        'sc_participants',
        'general_participants',
        'obc_participants',
        'photos', // Keep for backward compatibility
        'photo1',
        'photo2',
        'photo3',
        'photo4',
        'gis_location',
        // Zonal coordinator feedback fields
        'zc_pvent_preparedness_feedback',
        'zc_communication_effectiveness_feedback',
        'zc_participant_engagement_feedback',
        'zc_timeline_adherence_feedback',
        'zc_remark'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'planned_date' => 'datetime',
        'photos' => 'array',
        'required_coordinators' => 'array',
        'scientist_objectives_met' => 'array',
    ];

    /**
     * Get the scientist associated with this action plan.
     */
    public function scientist()
    {
        return $this->belongsTo(User::class, 'scientist_id');
    }

    /**
     * Get the district associated with this action plan.
     */
    public function district()
    {
        return $this->belongsTo(StateData::class, 'district_id');
    }

    /**
     * Get the participation requests associated with this action plan.
     */
    public function participationRequests()
    {
        return $this->hasMany(ParticipationRequest::class);
    }

    /**
     * Get all feedback submissions for this action plan.
     */
    public function feedback()
    {
        return $this->hasMany(ActionPlanFeedback::class);
    }
}
