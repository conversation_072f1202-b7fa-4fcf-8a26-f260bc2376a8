<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if the columns exist using MariaDB/MySQL syntax
        $columns = DB::select("SHOW COLUMNS FROM scientist_event_feedback");
        $columnNames = array_column($columns, 'Field');

        Schema::table('scientist_event_feedback', function (Blueprint $table) use ($columnNames) {
            // Only add columns if they don't already exist
            if (!in_array('event_success_rating', $columnNames)) {
                $table->integer('event_success_rating')->nullable()->after('success_rating')->comment('1-5 stars');
            }

            if (!in_array('challenges_faced', $columnNames)) {
                $table->text('challenges_faced')->nullable()->after('challenges');
            }

            if (!in_array('improvement_suggestions', $columnNames)) {
                $table->text('improvement_suggestions')->nullable()->after('suggestions');
            }

            if (!in_array('objectives_met_list', $columnNames)) {
                $table->json('objectives_met_list')->nullable()->after('objectives_met');
            }

            if (!in_array('self_assessed_learning', $columnNames)) {
                $table->text('self_assessed_learning')->nullable()->after('learning_outcome');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('scientist_event_feedback', function (Blueprint $table) {
            // Remove the added columns
            $table->dropColumn([
                'event_success_rating',
                'challenges_faced',
                'improvement_suggestions',
                'objectives_met_list',
                'self_assessed_learning',
            ]);
        });
    }
};
