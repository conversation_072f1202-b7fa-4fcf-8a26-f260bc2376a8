<?php

namespace App\Services;

use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use PHP<PERSON>ailer\PHPMailer\Exception;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    protected $mailer;
    
    /**
     * Create a new notification service instance.
     */
    public function __construct()
    {
        $this->mailer = new PHPMailer(true);
        
        // Configure SMTP
        $this->mailer->isSMTP();
        $this->mailer->Host = config('mail.mailers.smtp.host');
        $this->mailer->SMTPAuth = true;
        $this->mailer->Username = config('mail.mailers.smtp.username');
        $this->mailer->Password = config('mail.mailers.smtp.password');
        $this->mailer->SMTPSecure = config('mail.mailers.smtp.encryption');
        $this->mailer->Port = config('mail.mailers.smtp.port');
        
        // Set default sender
        $this->mailer->setFrom(config('mail.from.address'), config('mail.from.name'));
    }
    
    /**
     * Send a notification email.
     *
     * @param string|array $to Recipient email address(es)
     * @param string $subject Email subject
     * @param string $body Email body (HTML)
     * @param string|null $plainText Plain text version of the email
     * @param array $attachments Array of file paths to attach
     * @return bool Whether the email was sent successfully
     */
    public function sendEmail($to, string $subject, string $body, ?string $plainText = null, array $attachments = []): bool
    {
        try {
            // Reset mailer for new email
            $this->mailer->clearAddresses();
            $this->mailer->clearAttachments();
            
            // Add recipient(s)
            if (is_array($to)) {
                foreach ($to as $recipient) {
                    $this->mailer->addAddress($recipient);
                }
            } else {
                $this->mailer->addAddress($to);
            }
            
            // Set email content
            $this->mailer->isHTML(true);
            $this->mailer->Subject = $subject;
            $this->mailer->Body = $body;
            
            // Set plain text version if provided
            if ($plainText) {
                $this->mailer->AltBody = $plainText;
            }
            
            // Add attachments if any
            foreach ($attachments as $attachment) {
                if (file_exists($attachment)) {
                    $this->mailer->addAttachment($attachment);
                }
            }
            
            // Send the email
            $this->mailer->send();
            
            return true;
        } catch (Exception $e) {
            Log::error('Failed to send notification email: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send a new feedback notification.
     *
     * @param array $data Feedback data
     * @param string $recipientEmail Recipient email address
     * @return bool Whether the notification was sent successfully
     */
    public function sendNewFeedbackNotification(array $data, string $recipientEmail): bool
    {
        $subject = 'New Feedback Received - ' . ($data['event_title'] ?? 'Event');
        
        $body = $this->getEmailTemplate('new_feedback', [
            'event_title' => $data['event_title'] ?? 'Unknown Event',
            'participant_name' => $data['participant_name'] ?? 'Anonymous',
            'benefit_rating' => $data['benefit_rating'] ?? 'N/A',
            'speaker_rating' => $data['speaker_rating'] ?? 'N/A',
            'would_recommend' => isset($data['would_recommend']) && $data['would_recommend'] ? 'Yes' : 'No',
            'feedback_date' => date('Y-m-d H:i:s'),
            'dashboard_url' => route('zonal-coordinator.feedback-import.index'),
        ]);
        
        return $this->sendEmail($recipientEmail, $subject, $body);
    }
    
    /**
     * Send a feedback summary notification.
     *
     * @param array $data Summary data
     * @param string $recipientEmail Recipient email address
     * @return bool Whether the notification was sent successfully
     */
    public function sendFeedbackSummaryNotification(array $data, string $recipientEmail): bool
    {
        $subject = 'Feedback Summary - ' . ($data['event_title'] ?? 'All Events');
        
        $body = $this->getEmailTemplate('feedback_summary', [
            'event_title' => $data['event_title'] ?? 'All Events',
            'total_feedback' => $data['total_feedback'] ?? 0,
            'avg_benefit_rating' => $data['avg_benefit_rating'] ?? 0,
            'avg_speaker_rating' => $data['avg_speaker_rating'] ?? 0,
            'would_recommend_percentage' => $data['would_recommend_percentage'] ?? 0,
            'summary_date' => date('Y-m-d H:i:s'),
            'dashboard_url' => route('zonal-coordinator.feedback-analytics.index'),
        ]);
        
        // Attach PDF report if provided
        $attachments = [];
        if (isset($data['report_path']) && file_exists($data['report_path'])) {
            $attachments[] = $data['report_path'];
        }
        
        return $this->sendEmail($recipientEmail, $subject, $body, null, $attachments);
    }
    
    /**
     * Send a low rating alert notification.
     *
     * @param array $data Alert data
     * @param string $recipientEmail Recipient email address
     * @return bool Whether the notification was sent successfully
     */
    public function sendLowRatingAlertNotification(array $data, string $recipientEmail): bool
    {
        $subject = 'Low Rating Alert - ' . ($data['event_title'] ?? 'Event');
        
        $body = $this->getEmailTemplate('low_rating_alert', [
            'event_title' => $data['event_title'] ?? 'Unknown Event',
            'rating_type' => $data['rating_type'] ?? 'Overall',
            'rating_value' => $data['rating_value'] ?? 0,
            'threshold' => $data['threshold'] ?? 3,
            'alert_date' => date('Y-m-d H:i:s'),
            'dashboard_url' => route('zonal-coordinator.feedback-analytics.index'),
        ]);
        
        return $this->sendEmail($recipientEmail, $subject, $body);
    }
    
    /**
     * Get an email template with variables replaced.
     *
     * @param string $template Template name
     * @param array $variables Variables to replace in the template
     * @return string The processed template
     */
    protected function getEmailTemplate(string $template, array $variables = []): string
    {
        // Get the template content
        $templatePath = resource_path('views/emails/' . $template . '.blade.php');
        
        if (file_exists($templatePath)) {
            $content = file_get_contents($templatePath);
        } else {
            // Fallback to a basic template
            $content = $this->getDefaultTemplate();
        }
        
        // Replace variables
        foreach ($variables as $key => $value) {
            $content = str_replace('{{ $' . $key . ' }}', $value, $content);
        }
        
        return $content;
    }
    
    /**
     * Get a default email template.
     *
     * @return string The default template
     */
    protected function getDefaultTemplate(): string
    {
        return '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Notification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
        }
        .header {
            background-color: #4a6fdc;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        .footer {
            background-color: #f5f5f5;
            padding: 10px 20px;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
        .button {
            display: inline-block;
            background-color: #4a6fdc;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Mera Resham Mera Abhimaan</h1>
    </div>
    <div class="content">
        <h2>Notification</h2>
        <p>This is a notification from the Mera Resham Mera Abhimaan feedback system.</p>
        <p>Please check the dashboard for more details.</p>
        <a href="{{ $dashboard_url }}" class="button">View Dashboard</a>
    </div>
    <div class="footer">
        <p>&copy; ' . date('Y') . ' Mera Resham Mera Abhimaan. All rights reserved.</p>
    </div>
</body>
</html>';
    }
}
