<?php

// Load the Laravel environment
require __DIR__.'/vendor/autoload.php';
$app = require_once __DIR__.'/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// Get the columns in the action_plans table
$columns = DB::getSchemaBuilder()->getColumnListing('action_plans');

// Print the columns
echo "Columns in action_plans table:\n";
print_r($columns);

// Check if the photo columns exist
$photoColumns = ['photo1', 'photo2', 'photo3', 'photo4'];
$missingPhotoColumns = array_diff($photoColumns, $columns);

if (empty($missingPhotoColumns)) {
    echo "\nAll photo columns exist in the table.\n";
} else {
    echo "\nMissing photo columns: " . implode(', ', $missingPhotoColumns) . "\n";
}

// Check if the scientist feedback metrics columns exist
$feedbackColumns = [
    'event_success_rating',
    'challenges_faced',
    'suggestions_for_improvement',
    'objectives_met',
    'self_assessed_learning_outcome',
    'scientist_event_success_rating',
    'scientist_challenges_faced',
    'scientist_suggestions_for_improvement',
    'scientist_objectives_met',
    'scientist_self_assessed_learning_outcome'
];
$missingFeedbackColumns = array_diff($feedbackColumns, $columns);

if (empty($missingFeedbackColumns)) {
    echo "\nAll scientist feedback metrics columns exist in the table.\n";
} else {
    echo "\nMissing scientist feedback metrics columns: " . implode(', ', $missingFeedbackColumns) . "\n";
}
