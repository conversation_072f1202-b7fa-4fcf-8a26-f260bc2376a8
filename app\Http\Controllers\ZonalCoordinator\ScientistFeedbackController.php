<?php

namespace App\Http\Controllers\ZonalCoordinator;

use App\Http\Controllers\Controller;
use App\Models\ScientistFeedback;
use App\Models\User;
use App\Models\ZonalCoordinator;
use App\Models\StateData;
use App\Models\ActionPlan;
use App\Models\ActionPlanFeedback; // Ensure this model exists
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class ScientistFeedbackController extends Controller
{
    /**
     * Display the scientist feedback page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        return view('zonal-coordinator.scientist-feedback.index');
    }

    /**
     * Get all feedback entries created by the current zonal coordinator.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFeedback()
    {
        try {
            $user = Auth::user();
            Log::info('Getting feedback for user: ' . $user->id);

            $feedback = ActionPlan::whereHas('scientist', function ($query) use ($user) {
                $query->whereIn('district_id', $user->districts->pluck('id'));
            })
                ->whereNotNull('zc_pvent_preparedness_feedback')
                ->with(['scientist:id,name,email'])
                ->get()
                ->map(function ($plan) {
                    return [
                        'id' => $plan->id,
                        'scientist' => [
                            'id' => $plan->scientist->id,
                            'name' => $plan->scientist->name,
                            'email' => $plan->scientist->email,
                        ],
                        'feedback' => $plan->zc_pvent_preparedness_feedback,
                    ];
                });

            Log::info('Found ' . $feedback->count() . ' feedback entries');
            return response()->json($feedback);
        } catch (\Exception $e) {
            Log::error('Error getting feedback: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get feedback'], 500);
        }
    }

    /**
     * Get action plans in the coordinator's districts.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getEvents(Request $request)
    {
        try {
            $user = Auth::user();
            $zonalCoordinator = $user->zonalCoordinator;

            if (!$zonalCoordinator) {
                Log::warning('Zonal coordinator record not found for user: ' . $user->id);
                return response()->json([]);
            }

            // Get districts assigned to this coordinator
            $districtIds = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $zonalCoordinator->id)
                ->pluck('district_id');

            // Get all action plans for scientists in these districts
            $actionPlans = DB::table('action_plans')
                ->join('users', 'action_plans.scientist_id', '=', 'users.id')
                ->join('state_data', 'users.email', '=', 'state_data.scientist')
                ->whereIn('state_data.id', $districtIds)
                ->where('action_plans.status', 'completed')
                ->select(
                    'action_plans.id',
                    'action_plans.title',
                    'action_plans.planned_date',
                    'action_plans.status',
                    'action_plans.zc_pvent_preparedness_feedback',
                    'action_plans.zc_communication_effectiveness_feedback',
                    'action_plans.zc_participant_engagement_feedback',
                    'action_plans.zc_timeline_adherence_feedback',
                    'action_plans.zc_remark',
                    'users.name as scientist_name',
                    'users.email as scientist_email',
                    'state_data.status as scientist_type'
                )
                ->orderBy('action_plans.planned_date', 'desc')
                ->get();

            // Add has_feedback flag and calculate average rating
            $actionPlans = $actionPlans->map(function ($plan) {
                $ratings = [
                    $plan->zc_pvent_preparedness_feedback,
                    $plan->zc_communication_effectiveness_feedback,
                    $plan->zc_participant_engagement_feedback,
                    $plan->zc_timeline_adherence_feedback,
                ];

                $validRatings = array_filter($ratings, fn($rating) => !is_null($rating) && $rating !== '');
                $plan->has_feedback = !empty($validRatings);
                $plan->average_rating = !empty($validRatings) ? array_sum($validRatings) / count($validRatings) : 0;
                $plan->type = $plan->scientist_type;
                $plan->scientist = [
                    'name' => $plan->scientist_name,
                    'email' => $plan->scientist_email,
                ];
                return $plan;
            });

            Log::info('Found ' . $actionPlans->count() . ' action plans');
            return response()->json($actionPlans);
        } catch (\Exception $e) {
            Log::error('Error in getEvents: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get events'], 500);
        }
    }

    /**
     * Save feedback for a scientist.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveFeedback(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'action_plan_id' => 'required|exists:action_plans,id',
                // Scientist Performance Rating
                'preparedness_rating' => 'required|integer|min:1|max:5',
                'communication_rating' => 'required|integer|min:1|max:5',
                'engagement_rating' => 'required|integer|min:1|max:5',
                'adherence_rating' => 'required|integer|min:1|max:5',
                'remarks' => 'nullable|string|max:1000',
                // Action Plan Feedback
                'male_participants' => 'required|integer|min:0',
                'female_participants' => 'required|integer|min:0',
                'transgender_participants' => 'required|integer|min:0',
                'st_participants' => 'required|integer|min:0',
                'sc_participants' => 'required|integer|min:0',
                'general_participants' => 'required|integer|min:0',
                'obc_participants' => 'required|integer|min:0',
                'photo1' => 'required|image|max:2048',
                'photo2' => 'required|image|max:2048',
                'photo3' => 'required|image|max:2048',
                'photo4' => 'required|image|max:2048',
                'gis_location' => ['required', 'regex:/^[-+]?([1-8]?\d(\.\d+)?|90(\.0+)?),\s*[-+]?(180(\.0+)?|((1[0-7]\d)|([1-9]?\d))(\.\d+)?)$/'],
                'event_success_rating' => 'required|integer|min:1|max:5',
                'challenges_faced' => 'required|string',
                'suggestions_for_improvement' => 'required|string',
                'objectives_met' => 'required|array',
                'self_assessed_learning_outcome' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json(['error' => $validator->errors()], 422);
            }

            DB::beginTransaction();

            // Update action plan with scientist performance ratings
            $actionPlan = ActionPlan::findOrFail($request->action_plan_id);
            $actionPlan->update([
                'zc_pvent_preparedness_feedback' => $request->preparedness_rating,
                'zc_communication_effectiveness_feedback' => $request->communication_rating,
                'zc_participant_engagement_feedback' => $request->engagement_rating,
                'zc_timeline_adherence_feedback' => $request->adherence_rating,
                'zc_remark' => $request->remarks,
                'zc_feedback_status' => 'submitted',
            ]);

            // Store photos
            $photos = [];
            $existingFeedback = ActionPlanFeedback::where('action_plan_id', $request->action_plan_id)->first();

            foreach (['photo1', 'photo2', 'photo3', 'photo4'] as $key) {
                if ($request->hasFile($key)) {
                    // Delete old photo if it exists and a new one is uploaded
                    if ($existingFeedback && $existingFeedback->$key) {
                        Storage::disk('public')->delete($existingFeedback->$key);
                    }
                    $path = $request->file($key)->store('action-plan-photos', 'public');
                    $photos[$key] = $path;
                } elseif ($existingFeedback && $existingFeedback->$key) {
                    // Keep existing photo if no new one is uploaded
                    $photos[$key] = $existingFeedback->$key;
                }
            }

            // Create or update action plan feedback
            ActionPlanFeedback::updateOrCreate(
                ['action_plan_id' => $request->action_plan_id, 'user_id' => Auth::id()], // Conditions to find existing record
                [
                    'male_participants' => $request->male_participants,
                    'female_participants' => $request->female_participants,
                    'transgender_participants' => $request->transgender_participants,
                    'st_participants' => $request->st_participants,
                    'sc_participants' => $request->sc_participants,
                    'general_participants' => $request->general_participants,
                    'obc_participants' => $request->obc_participants,
                    'photo1' => $photos['photo1'] ?? null,
                    'photo2' => $photos['photo2'] ?? null,
                    'photo3' => $photos['photo3'] ?? null,
                    'photo4' => $photos['photo4'] ?? null,
                    'gis_location' => $request->gis_location,
                    'event_success_rating' => $request->event_success_rating,
                    'challenges_faced' => $request->challenges_faced,
                    'suggestions_for_improvement' => $request->suggestions_for_improvement,
                    'objectives_met' => json_encode($request->objectives_met),
                    'self_assessed_learning_outcome' => $request->self_assessed_learning_outcome,
                ]
            );

            DB::commit();
            return response()->json(['success' => true, 'message' => 'Feedback saved successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error saving feedback: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to save feedback'], 500);
        }
    }

    /**
     * Delete feedback.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteFeedback($id)
    {
        try {
            $feedback = ScientistFeedback::where('id', $id)
                ->where('created_by', Auth::id())
                ->first();

            if (!$feedback) {
                return response()->json(['error' => 'Feedback not found or you do not have permission to delete it'], 404);
            }

            $feedback->delete();
            return response()->json(['message' => 'Feedback deleted successfully']);
        } catch (\Exception $e) {
            Log::error('Error deleting feedback: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete feedback'], 500);
        }
    }

    /**
     * Get feedback details.
     *
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFeedbackDetails($id)
    {
        try {
            // Check if it's an action plan feedback
            if (strpos($id, 'ap_') === 0) {
                $actionPlanId = substr($id, 3);
                $feedback = ActionPlan::with(['scientist:id,name,email', 'district:id,state,district'])
                    ->where('id', $actionPlanId)
                    ->first();

                if (!$feedback) {
                    return response()->json(['error' => 'Feedback not found'], 404);
                }

                return response()->json([
                    'id' => 'ap_' . $feedback->id,
                    'type' => 'action_plan',
                    'scientist' => $feedback->scientist,
                    'event' => [
                        'id' => $feedback->id,
                        'title' => $feedback->title,
                        'start_date' => $feedback->planned_date,
                    ],
                    'overall_rating' => $feedback->scientist_event_success_rating,
                    'detailed_ratings' => [
                        'challenges' => $feedback->scientist_challenges_faced,
                        'suggestions' => $feedback->scientist_suggestions_for_improvement,
                        'objectives' => $feedback->scientist_objectives_met,
                        'learning_outcome' => $feedback->scientist_self_assessed_learning_outcome,
                    ],
                    'created_at' => $feedback->updated_at,
                ]);
            }
            // It's a coordinator feedback
            elseif (strpos($id, 'sf_') === 0) {
                $feedbackId = substr($id, 3);
                $feedback = ScientistFeedback::with(['scientist:id,name,email', 'event:id,title,start_date'])
                    ->where('id', $feedbackId)
                    ->where('created_by', Auth::id())
                    ->first();

                if (!$feedback) {
                    return response()->json(['error' => 'Feedback not found'], 404);
                }

                return response()->json([
                    'id' => 'sf_' . $feedback->id,
                    'type' => 'coordinator_feedback',
                    'scientist' => $feedback->scientist,
                    'event' => $feedback->event,
                    'overall_rating' => $feedback->overall_rating,
                    'detailed_ratings' => [
                        'preparedness' => $feedback->preparedness_rating,
                        'communication' => $feedback->communication_rating,
                        'engagement' => $feedback->engagement_rating,
                        'adherence' => $feedback->adherence_rating,
                        'remarks' => $feedback->remarks,
                    ],
                    'created_at' => $feedback->created_at,
                ]);
            }

            return response()->json(['error' => 'Invalid feedback ID'], 400);
        } catch (\Exception $e) {
            Log::error('Error getting feedback details: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get feedback details'], 500);
        }
    }
}
