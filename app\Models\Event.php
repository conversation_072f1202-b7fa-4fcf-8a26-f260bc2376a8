<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * This is a stub model for Event.
 * The actual events functionality has been removed and replaced with action plans.
 * This stub exists only to prevent errors in code that still references the Event model.
 */
class Event extends Model
{
    use HasFactory;

    protected $table = 'action_plans';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'title',
        'description',
        'location',
        'district_id',
        'scientist_id',
        'expected_participants',
        'actual_participants',
        'participant_demographics',
        'topic',
        'status',
        // Action plan specific fields
        'type',
        'planned_date',
        'required_coordinators',
        'cancellation_reason',
        'male_participants',
        'female_participants',
        'transgender_participants',
        'st_participants',
        'sc_participants',
        'general_participants',
        'obc_participants',
        'photos',
        'photo1',
        'photo2',
        'photo3',
        'photo4',
        'gis_location',
        'scientist_event_success_rating',
        'scientist_challenges_faced',
        'scientist_suggestions_for_improvement',
        'scientist_objectives_met',
        'scientist_self_assessed_learning_outcome',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'planned_date' => 'datetime',
        'participant_demographics' => 'array',
        'photos' => 'array',
        'required_coordinators' => 'array',
        'scientist_objectives_met' => 'array',
    ];

    /**
     * Get the start_date attribute.
     * This is an accessor to maintain backward compatibility with code that uses start_date.
     */
    public function getStartDateAttribute()
    {
        return $this->planned_date;
    }

    /**
     * Get the end_date attribute.
     * This is an accessor to maintain backward compatibility with code that uses end_date.
     */
    public function getEndDateAttribute()
    {
        return $this->planned_date;
    }

    /**
     * Set the start_date attribute.
     * This is a mutator to maintain backward compatibility with code that uses start_date.
     */
    public function setStartDateAttribute($value)
    {
        $this->planned_date = $value;
    }

    /**
     * Set the end_date attribute.
     * This is a mutator to maintain backward compatibility with code that uses end_date.
     */
    public function setEndDateAttribute($value)
    {
        $this->planned_date = $value;
    }

    /**
     * Get the district associated with this event.
     */
    public function district()
    {
        return $this->belongsTo(StateData::class, 'district_id', 'id');
    }

    /**
     * Get the scientist associated with this event.
     */
    public function scientist()
    {
        return $this->belongsTo(User::class, 'scientist_id');
    }

    /**
     * Get the feedback for this event.
     */
    public function feedback()
    {
        return $this->hasOne(ScientistEventFeedback::class, 'action_plan_id', 'id');
    }
}
