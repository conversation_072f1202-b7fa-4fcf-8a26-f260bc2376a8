@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>{{ __('QR Code for Public Reviews') }}</span>
                        <div>
                            <a href="{{ route('scientist.feedback-import.index') }}" class="btn btn-sm btn-secondary">Back to Review System</a>
                        </div>
                    </div>
                </div>

                <div class="card-body text-center">
                    <!-- Action Plan Details -->
                    <div class="alert alert-info">
                        <h5>{{ $actionPlan->title }}</h5>
                        <p><strong>Date:</strong> {{ $actionPlan->planned_date ? $actionPlan->planned_date->format('d M Y') : 'N/A' }}</p>
                        <p><strong>Location:</strong> {{ $actionPlan->location ?? 'N/A' }}</p>
                    </div>

                    <!-- QR Code -->
                    <div class="mb-4">
                        <h4>Scan to Submit Feedback</h4>
                        <div class="d-flex justify-content-center mb-3">
                            {!! $qrCode !!}
                        </div>
                        <p class="text-muted">Participants can scan this QR code to submit feedback without logging in</p>
                    </div>

                    <!-- Feedback URL -->
                    <div class="mb-4">
                        <h5>Direct Link</h5>
                        <div class="input-group">
                            <input type="text" class="form-control" id="feedbackUrl" value="{{ $feedbackUrl }}" readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard()">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                        </div>
                        <small class="text-muted">You can also share this link directly with participants</small>
                    </div>

                    <!-- Instructions -->
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-clock"></i> Time Limit</h6>
                        <p class="mb-0">This QR code is valid for 2 days from the event date. After that, public feedback submission will be disabled.</p>
                    </div>

                    <!-- Actions -->
                    <div class="mt-4">
                        <button class="btn btn-primary" onclick="printQR()">
                            <i class="fas fa-print"></i> Print QR Code
                        </button>
                        <button class="btn btn-success" onclick="downloadQR()">
                            <i class="fas fa-download"></i> Download QR Code
                        </button>
                        <a href="{{ $feedbackUrl }}" class="btn btn-info" target="_blank">
                            <i class="fas fa-external-link-alt"></i> Test Feedback Form
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    function copyToClipboard() {
        const urlInput = document.getElementById('feedbackUrl');
        urlInput.select();
        urlInput.setSelectionRange(0, 99999); // For mobile devices
        
        try {
            document.execCommand('copy');
            alert('Link copied to clipboard!');
        } catch (err) {
            console.error('Failed to copy: ', err);
            alert('Failed to copy link. Please copy manually.');
        }
    }

    function printQR() {
        window.print();
    }

    function downloadQR() {
        // Create a canvas from the SVG
        const svg = document.querySelector('svg');
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // Set canvas size
        canvas.width = 300;
        canvas.height = 300;
        
        // Convert SVG to data URL
        const data = new XMLSerializer().serializeToString(svg);
        const svgBlob = new Blob([data], {type: 'image/svg+xml;charset=utf-8'});
        const url = URL.createObjectURL(svgBlob);
        
        // Create image and draw to canvas
        const img = new Image();
        img.onload = function() {
            ctx.drawImage(img, 0, 0);
            
            // Download the canvas as PNG
            const link = document.createElement('a');
            link.download = 'qr-code-{{ $actionPlan->id }}.png';
            link.href = canvas.toDataURL();
            link.click();
            
            URL.revokeObjectURL(url);
        };
        img.src = url;
    }
</script>

@section('styles')
<style>
    @media print {
        .card-header, .btn, .alert-warning, .input-group {
            display: none !important;
        }
        
        .card {
            border: none !important;
            box-shadow: none !important;
        }
        
        .container {
            max-width: 100% !important;
        }
    }
</style>
@endsection
@endsection
