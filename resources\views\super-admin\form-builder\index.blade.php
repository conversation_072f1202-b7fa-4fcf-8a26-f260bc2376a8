@extends('layouts.app')

@section('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
<style>
    .field-preview {
        transition: all 0.2s ease;
    }
    .field-preview:hover {
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .condition-row {
        background-color: #f8f9fa;
    }
</style>
@endsection

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Custom Form Builder') }}</h5>
                    <div>
                        <button id="addFormBtn" class="btn btn-sm btn-success me-2">
                            <i class="bi bi-plus-circle"></i> Create New Form
                        </button>
                        <button id="createPredefinedBtn" class="btn btn-sm btn-primary me-2">
                            <i class="bi bi-file-earmark-plus"></i> Create Predefined Forms
                        </button>
                        <a href="{{ route('super-admin.dashboard') }}" class="btn btn-sm btn-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p><strong>Instructions:</strong></p>
                        <ul>
                            <li>Create and manage custom forms for different profiles.</li>
                            <li>Use the "Create Predefined Forms" button to generate the standard forms required by the system.</li>
                            <li>Click on a form to edit its structure or create a new form from scratch.</li>
                        </ul>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Form Name</th>
                                    <th>Target Users</th>
                                    <th>Fields</th>
                                    <th>Last Date</th>
                                    <th>Submissions</th>
                                    <th>Last Updated</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="formsTableBody">
                                <!-- Forms will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <div id="pagination" class="mt-3">
                        <!-- Pagination will be added here -->
                    </div>

                    <!-- Form Builder Modal -->
                    <div class="modal fade" id="formBuilderModal" tabindex="-1" aria-labelledby="formBuilderModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-xl">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="formBuilderModalLabel">Create New Form</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h5>Form Properties</h5>
                                                </div>
                                                <div class="card-body">
                                                    <form id="formPropertiesForm">
                                                        <input type="hidden" id="formId" name="id">

                                                        <div class="form-group mb-3">
                                                            <label for="form_name">Form Name:</label>
                                                            <input type="text" class="form-control" id="form_name" name="form_name" required>
                                                        </div>

                                                        <div class="form-group mb-3">
                                                            <label for="target_user">Target Users:</label>
                                                            <select class="form-control" id="target_user" name="target_user" required>
                                                                <option value="all_scientists">All Scientists</option>
                                                                <option value="pre_cocoon">Pre-Cocoon Scientists</option>
                                                                <option value="post_cocoon">Post-Cocoon Scientists</option>
                                                            </select>
                                                            <small class="form-text text-muted">Select which scientists will be able to fill this form</small>
                                                        </div>

                                                        <div class="form-group mb-3">
                                                            <label for="last_date">Application Last Date and Time:</label>
                                                            <input type="datetime-local" class="form-control" id="last_date" name="last_date">
                                                            <small class="form-text text-muted">Deadline for form submission (optional)</small>
                                                        </div>

                                                        <hr>

                                                        <h6>Add Field</h6>
                                                        <div class="form-group mb-3">
                                                            <label for="field_type">Field Type:</label>
                                                            <select class="form-control" id="field_type">
                                                                <optgroup label="Basic Inputs">
                                                                    <option value="text">Text Input</option>
                                                                    <option value="textarea">Text Area</option>
                                                                    <option value="number">Number Input</option>
                                                                    <option value="email">Email Input</option>
                                                                    <option value="password">Password Input</option>
                                                                    <option value="tel">Phone Number</option>
                                                                    <option value="url">URL Input</option>
                                                                </optgroup>
                                                                <optgroup label="Advanced Inputs">
                                                                    <option value="file">File Upload</option>
                                                                    <option value="rich-text">Rich Text Editor</option>
                                                                    <option value="color">Color Picker</option>
                                                                    <option value="range">Range Slider</option>
                                                                </optgroup>
                                                                <optgroup label="Selection Inputs">
                                                                    <option value="select">Dropdown Select</option>
                                                                    <option value="radio">Radio Buttons</option>
                                                                    <option value="checkbox">Checkboxes</option>
                                                                </optgroup>
                                                                <optgroup label="Date & Time">
                                                                    <option value="date">Date Picker</option>
                                                                    <option value="time">Time Picker</option>
                                                                    <option value="datetime-local">Date & Time Picker</option>
                                                                </optgroup>
                                                            </select>
                                                        </div>

                                                        <div class="form-group mb-3">
                                                            <label for="field_label">Field Label:</label>
                                                            <input type="text" class="form-control" id="field_label">
                                                        </div>

                                                        <div class="form-group mb-3">
                                                            <label for="field_placeholder">Placeholder (optional):</label>
                                                            <input type="text" class="form-control" id="field_placeholder">
                                                        </div>

                                                        <div class="form-group mb-3" id="optionsContainer" style="display: none;">
                                                            <label for="field_options">Options (one per line):</label>
                                                            <textarea class="form-control" id="field_options" rows="4"></textarea>
                                                        </div>

                                                        <div class="form-group mb-3" id="rangeContainer" style="display: none;">
                                                            <div class="row">
                                                                <div class="col-md-6">
                                                                    <label for="field_min">Min Value:</label>
                                                                    <input type="number" class="form-control" id="field_min" value="0">
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <label for="field_max">Max Value:</label>
                                                                    <input type="number" class="form-control" id="field_max" value="100">
                                                                </div>
                                                            </div>
                                                            <div class="mt-2">
                                                                <label for="field_step">Step:</label>
                                                                <input type="number" class="form-control" id="field_step" value="1">
                                                            </div>
                                                        </div>

                                                        <div class="form-group mb-3" id="fileContainer" style="display: none;">
                                                            <label for="field_accept">Accepted File Types:</label>
                                                            <select class="form-control" id="field_accept">
                                                                <option value="">All Files</option>
                                                                <option value="image/*">Images Only</option>
                                                                <option value=".pdf,.doc,.docx">Documents (PDF, DOC)</option>
                                                                <option value=".csv,.xls,.xlsx">Spreadsheets</option>
                                                                <option value="custom">Custom...</option>
                                                            </select>
                                                            <div id="customAcceptContainer" class="mt-2" style="display: none;">
                                                                <input type="text" class="form-control" id="field_custom_accept"
                                                                    placeholder="e.g. .jpg,.png,.pdf">
                                                                <small class="form-text text-muted">Comma-separated file extensions or MIME types</small>
                                                            </div>
                                                        </div>

                                                        <div class="form-check mb-3">
                                                            <input type="checkbox" class="form-check-input" id="field_required">
                                                            <label class="form-check-label" for="field_required">Required Field</label>
                                                        </div>

                                                        <div class="form-check mb-3">
                                                            <input type="checkbox" class="form-check-input" id="field_full_width">
                                                            <label class="form-check-label" for="field_full_width">Full Width (span both columns)</label>
                                                        </div>

                                                        <hr>

                                                        <div class="mb-3">
                                                            <label class="form-label d-flex justify-content-between align-items-center">
                                                                <span>Conditional Logic</span>
                                                                <div class="form-check form-switch">
                                                                    <input class="form-check-input" type="checkbox" id="enable_conditions">
                                                                </div>
                                                            </label>
                                                            <div id="conditionsContainer" style="display: none;">
                                                                <div class="alert alert-info small">
                                                                    Show this field only when the following conditions are met:
                                                                </div>
                                                                <div id="conditionsList">
                                                                    <!-- Conditions will be added here -->
                                                                </div>
                                                                <button type="button" class="btn btn-sm btn-outline-primary mt-2" id="addConditionBtn">
                                                                    <i class="bi bi-plus-circle"></i> Add Condition
                                                                </button>
                                                            </div>
                                                        </div>

                                                        <button type="button" class="btn btn-primary w-100" id="addFieldBtn">
                                                            <i class="bi bi-plus-lg"></i> Add Field
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-8">
                                            <div class="card">
                                                <div class="card-header d-flex justify-content-between align-items-center">
                                                    <h5>Form Preview</h5>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" id="clearFormBtn">Clear All Fields</button>
                                                </div>
                                                <div class="card-body">
                                                    <div id="formPreview" class="border p-3 rounded">
                                                        <p class="text-muted text-center">Add fields to see the form preview here.</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                        <i class="bi bi-x-circle"></i> Close
                                    </button>
                                    <button type="button" class="btn btn-primary" id="saveFormBtn">
                                        <i class="bi bi-save"></i> Save Form
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Delete Confirmation Modal -->
                    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="deleteConfirmModalLabel">Confirm Delete</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <p>Are you sure you want to delete this form? This action cannot be undone.</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                        <i class="bi bi-x-circle"></i> Cancel
                                    </button>
                                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                                        <i class="bi bi-trash"></i> Delete Form
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const formsTableBody = document.getElementById('formsTableBody');
        const addFormBtn = document.getElementById('addFormBtn');
        const createPredefinedBtn = document.getElementById('createPredefinedBtn');
        const formPropertiesForm = document.getElementById('formPropertiesForm');
        const saveFormBtn = document.getElementById('saveFormBtn');
        const formBuilderModal = new bootstrap.Modal(document.getElementById('formBuilderModal'));
        const deleteConfirmModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
        const fieldTypeSelect = document.getElementById('field_type');
        const optionsContainer = document.getElementById('optionsContainer');
        const addFieldBtn = document.getElementById('addFieldBtn');
        const formPreview = document.getElementById('formPreview');
        const clearFormBtn = document.getElementById('clearFormBtn');
        let currentFormId = null;
        let formFields = [];

        // Load forms
        loadForms();

        // Field type change event
        fieldTypeSelect.addEventListener('change', function() {
            toggleFieldOptions();
        });

        // Add form button click
        addFormBtn.addEventListener('click', function() {
            resetFormBuilder();
            document.getElementById('formBuilderModalLabel').textContent = 'Create New Form';
            formBuilderModal.show();
        });

        // Enable conditions toggle
        document.getElementById('enable_conditions').addEventListener('change', function() {
            document.getElementById('conditionsContainer').style.display = this.checked ? 'block' : 'none';
        });

        // Add condition button click
        document.getElementById('addConditionBtn').addEventListener('click', function() {
            addConditionRow();
        });

        // File accept type change
        document.getElementById('field_accept').addEventListener('change', function() {
            document.getElementById('customAcceptContainer').style.display =
                this.value === 'custom' ? 'block' : 'none';
        });

        // Create predefined forms button click
        createPredefinedBtn.addEventListener('click', function() {
            if (confirm('This will create or update the standard predefined forms. Continue?')) {
                fetch('{{ route("super-admin.form-builder.create-predefined") }}', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.message) {
                        alert(data.message);
                        loadForms();
                    } else if (data.error) {
                        alert(data.error);
                    }
                })
                .catch(error => {
                    console.error('Error creating predefined forms:', error);
                    alert('An error occurred while creating predefined forms.');
                });
            }
        });

        // Add field button click
        addFieldBtn.addEventListener('click', function() {
            const fieldType = fieldTypeSelect.value;
            const fieldLabel = document.getElementById('field_label').value.trim();
            const fieldPlaceholder = document.getElementById('field_placeholder').value.trim();
            const fieldRequired = document.getElementById('field_required').checked;
            const fieldFullWidth = document.getElementById('field_full_width').checked;
            let fieldOptions = [];

            if (!fieldLabel) {
                alert('Field label is required.');
                return;
            }

            // Validate options for select, radio, checkbox
            if (['select', 'radio', 'checkbox'].includes(fieldType)) {
                const optionsText = document.getElementById('field_options').value.trim();
                if (!optionsText) {
                    alert('Options are required for this field type.');
                    return;
                }
                fieldOptions = optionsText.split('\n').filter(option => option.trim() !== '').map(option => option.trim());
                if (fieldOptions.length === 0) {
                    alert('At least one option is required.');
                    return;
                }
            }

            // Create the base field object
            const newField = {
                type: fieldType,
                label: fieldLabel,
                required: fieldRequired,
                placeholder: fieldPlaceholder || undefined,
                fullWidth: fieldFullWidth
            };

            // Add options for select, radio, checkbox
            if (fieldOptions.length > 0) {
                newField.options = fieldOptions;
            }

            // Add range properties
            if (fieldType === 'range') {
                newField.min = document.getElementById('field_min').value;
                newField.max = document.getElementById('field_max').value;
                newField.step = document.getElementById('field_step').value;
            }

            // Add file upload properties
            if (fieldType === 'file') {
                const acceptType = document.getElementById('field_accept').value;
                if (acceptType === 'custom') {
                    newField.accept = document.getElementById('field_custom_accept').value;
                } else {
                    newField.accept = acceptType;
                }
            }

            // Add conditional logic
            if (document.getElementById('enable_conditions').checked) {
                const conditions = [];
                document.querySelectorAll('.condition-row').forEach(row => {
                    const fieldIndex = row.querySelector('.condition-field').value;
                    const operator = row.querySelector('.condition-operator').value;
                    const value = row.querySelector('.condition-value').value;

                    if (fieldIndex && operator) {
                        conditions.push({
                            field: parseInt(fieldIndex),
                            operator: operator,
                            value: value
                        });
                    }
                });

                if (conditions.length > 0) {
                    newField.conditions = conditions;
                }
            }

            formFields.push(newField);
            updateFormPreview();

            // Reset field inputs
            document.getElementById('field_label').value = '';
            document.getElementById('field_placeholder').value = '';
            document.getElementById('field_required').checked = false;
            document.getElementById('field_full_width').checked = false;
            document.getElementById('field_options').value = '';
            document.getElementById('enable_conditions').checked = false;
            document.getElementById('conditionsContainer').style.display = 'none';
            document.getElementById('conditionsList').innerHTML = '';

            if (fieldType === 'range') {
                document.getElementById('field_min').value = '0';
                document.getElementById('field_max').value = '100';
                document.getElementById('field_step').value = '1';
            }

            if (fieldType === 'file') {
                document.getElementById('field_accept').value = '';
                document.getElementById('field_custom_accept').value = '';
                document.getElementById('customAcceptContainer').style.display = 'none';
            }
        });

        // Clear form button click
        clearFormBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to clear all fields from this form?')) {
                formFields = [];
                updateFormPreview();
            }
        });

        // Save form button click
        saveFormBtn.addEventListener('click', function() {
            const formName = document.getElementById('form_name').value.trim();

            if (!formName) {
                alert('Form name is required.');
                return;
            }

            if (formFields.length === 0) {
                alert('At least one field is required.');
                return;
            }

            const targetUser = document.getElementById('target_user').value;
            const lastDate = document.getElementById('last_date').value;

            const formData = {
                form_name: formName,
                target_user: targetUser,
                form_structure: JSON.stringify(formFields),
                last_date: lastDate || null
            };

            if (currentFormId) {
                formData.id = currentFormId;

                fetch(`{{ url("super-admin/form-builder/update") }}/${currentFormId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.message) {
                        alert(data.message);
                        formBuilderModal.hide();
                        loadForms();
                    } else if (data.error) {
                        alert(data.error);
                    }
                })
                .catch(error => {
                    console.error('Error updating form:', error);
                    alert('An error occurred while updating the form.');
                });
            } else {
                fetch('{{ route("super-admin.form-builder.create") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.message) {
                        alert(data.message);
                        formBuilderModal.hide();
                        loadForms();
                    } else if (data.error) {
                        alert(data.error);
                    }
                })
                .catch(error => {
                    console.error('Error creating form:', error);
                    alert('An error occurred while creating the form.');
                });
            }
        });

        // Confirm delete button click
        confirmDeleteBtn.addEventListener('click', function() {
            if (currentFormId) {
                fetch(`{{ url("super-admin/form-builder/delete") }}/${currentFormId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.message) {
                        alert(data.message);
                        deleteConfirmModal.hide();
                        loadForms();
                    } else if (data.error) {
                        alert(data.error);
                    }
                })
                .catch(error => {
                    console.error('Error deleting form:', error);
                    alert('An error occurred while deleting the form.');
                });
            }
        });

        // Global variables for pagination
        let currentPage = 1;
        let totalForms = 0;
        let formsPerPage = 50;
        let allForms = [];

        // Load forms function
        function loadForms() {
            fetch('{{ route("super-admin.form-builder.forms") }}')
                .then(response => response.json())
                .then(data => {
                    // Store all forms and sort by newest first
                    allForms = data.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
                    totalForms = allForms.length;

                    // Display first page
                    displayFormsPage(1);

                    // Setup pagination
                    setupPagination();
                })
                .catch(error => {
                    console.error('Error loading forms:', error);
                    formsTableBody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">Error loading forms. Please try again later.</td></tr>';
                });
        }

        // Display forms for a specific page
        function displayFormsPage(page) {
            currentPage = page;
            formsTableBody.innerHTML = '';

            if (allForms.length === 0) {
                formsTableBody.innerHTML = '<tr><td colspan="7" class="text-center">No forms available. Create your first form!</td></tr>';
                return;
            }

            // Calculate start and end indices
            const startIndex = (page - 1) * formsPerPage;
            const endIndex = Math.min(startIndex + formsPerPage, totalForms);

            // Get forms for current page
            const formsForPage = allForms.slice(startIndex, endIndex);

            // Display forms
            formsForPage.forEach(form => {
                const row = document.createElement('tr');

                // Parse form structure - handle both string and array cases
                let formStructure = [];
                try {
                    if (typeof form.form_structure === 'string') {
                        // It's a JSON string, parse it
                        formStructure = JSON.parse(form.form_structure);
                    } else if (Array.isArray(form.form_structure)) {
                        // It's already an array (from the backend accessor)
                        formStructure = form.form_structure;
                    } else {
                        console.warn('Unexpected form_structure type:', typeof form.form_structure);
                        formStructure = [];
                    }
                } catch (e) {
                    console.error('Error parsing form structure:', e);
                    formStructure = [];
                }

                // Format updated date
                const updatedDate = new Date(form.updated_at);
                const formattedUpdatedDate = updatedDate.toLocaleDateString() + ' ' + updatedDate.toLocaleTimeString();

                // Format last date if exists
                let lastDateDisplay = 'No deadline';
                if (form.last_date) {
                    const lastDate = new Date(form.last_date);
                    lastDateDisplay = lastDate.toLocaleDateString() + ' ' + lastDate.toLocaleTimeString();
                }

                // Format target user display
                let targetUserDisplay = 'All Scientists';
                if (form.target_user === 'pre_cocoon') {
                    targetUserDisplay = 'Pre-Cocoon Scientists';
                } else if (form.target_user === 'post_cocoon') {
                    targetUserDisplay = 'Post-Cocoon Scientists';
                }

                // Get submission count (placeholder - will be updated by AJAX)
                const submissionCount = form.submission_count || 0;

                row.innerHTML = `
                    <td>${form.form_name}</td>
                    <td><span class="badge bg-info">${targetUserDisplay}</span></td>
                    <td>${formStructure.length} fields</td>
                    <td>${lastDateDisplay}</td>
                    <td>
                        <span class="badge bg-secondary">${submissionCount}</span>
                        <button class="btn btn-sm btn-info view-submissions-btn" data-id="${form.id}">
                            <i class="bi bi-eye"></i> View
                        </button>
                    </td>
                    <td>${formattedUpdatedDate}</td>
                    <td>
                        <button class="btn btn-sm btn-primary edit-btn" data-id="${form.id}">
                            <i class="bi bi-pencil"></i> Edit
                        </button>
                        <button class="btn btn-sm btn-danger delete-btn" data-id="${form.id}">
                            <i class="bi bi-trash"></i> Delete
                        </button>
                    </td>
                `;

                formsTableBody.appendChild(row);

                // Fetch submission count for this form
                fetchSubmissionCount(form.id);
            });

            // Add event listeners to buttons
            addButtonEventListeners();
        }

        // Fetch submission count for a form
        function fetchSubmissionCount(formId) {
            fetch(`{{ url('super-admin/form-builder/submission-count') }}/${formId}`)
                .then(response => response.json())
                .then(data => {
                    // Update the submission count in the table
                    const submissionBadge = document.querySelector(`button.view-submissions-btn[data-id="${formId}"]`)
                        .previousElementSibling;
                    submissionBadge.textContent = data.count;

                    // Update the count in our local data
                    const formIndex = allForms.findIndex(f => f.id == formId);
                    if (formIndex !== -1) {
                        allForms[formIndex].submission_count = data.count;
                    }
                })
                .catch(error => {
                    console.error(`Error fetching submission count for form ${formId}:`, error);
                });
        }

        // Setup pagination controls
        function setupPagination() {
            const totalPages = Math.ceil(totalForms / formsPerPage);

            // If only one page or no forms, don't show pagination
            if (totalPages <= 1) {
                document.getElementById('pagination').innerHTML = '';
                return;
            }

            let paginationHtml = `
                <nav aria-label="Form pagination">
                    <ul class="pagination justify-content-center">
                        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                            <a class="page-link" href="#" data-page="${currentPage - 1}">Previous</a>
                        </li>
            `;

            // Add page numbers
            for (let i = 1; i <= totalPages; i++) {
                paginationHtml += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>
                `;
            }

            paginationHtml += `
                        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                            <a class="page-link" href="#" data-page="${currentPage + 1}">Next</a>
                        </li>
                    </ul>
                </nav>
            `;

            document.getElementById('pagination').innerHTML = paginationHtml;

            // Add event listeners to pagination links
            document.querySelectorAll('#pagination .page-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const page = parseInt(this.getAttribute('data-page'));
                    if (page >= 1 && page <= totalPages) {
                        displayFormsPage(page);
                        setupPagination();
                    }
                });
            });
        }

        // Add event listeners to buttons
        function addButtonEventListeners() {
            document.querySelectorAll('.edit-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const formId = this.dataset.id;
                    loadFormDetails(formId);
                });
            });

            document.querySelectorAll('.delete-btn').forEach(button => {
                button.addEventListener('click', function() {
                    currentFormId = this.dataset.id;
                    deleteConfirmModal.show();
                });
            });

            document.querySelectorAll('.view-submissions-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const formId = this.dataset.id;
                    window.location.href = `{{ url('super-admin/form-builder/submissions') }}/${formId}`;
                });
            });
        }

        // Load form details function
        function loadFormDetails(id) {
            fetch(`{{ url("super-admin/form-builder/form") }}/${id}`)
                .then(response => response.json())
                .then(form => {
                    resetFormBuilder();

                    currentFormId = form.id;
                    document.getElementById('formId').value = form.id;
                    document.getElementById('form_name').value = form.form_name;
                    document.getElementById('target_user').value = form.target_user || 'all_scientists';

                    // Format the last_date for datetime-local input
                    if (form.last_date) {
                        const lastDate = new Date(form.last_date);
                        const formattedDate = lastDate.toISOString().slice(0, 16);
                        document.getElementById('last_date').value = formattedDate;
                    } else {
                        document.getElementById('last_date').value = '';
                    }

                    document.getElementById('formBuilderModalLabel').textContent = 'Edit Form';

                    // Parse form structure - handle both string and array cases
                    try {
                        if (typeof form.form_structure === 'string') {
                            // It's a JSON string, parse it
                            formFields = JSON.parse(form.form_structure);
                        } else if (Array.isArray(form.form_structure)) {
                            // It's already an array (from the backend accessor)
                            formFields = form.form_structure;
                        } else {
                            console.warn('Unexpected form_structure type in edit:', typeof form.form_structure);
                            formFields = [];
                        }
                        updateFormPreview();
                    } catch (e) {
                        console.error('Error parsing form structure:', e);
                        formFields = [];
                    }

                    formBuilderModal.show();
                })
                .catch(error => console.error('Error loading form details:', error));
        }

        // Toggle field options based on field type
        function toggleFieldOptions() {
            const fieldType = fieldTypeSelect.value;

            // Options container (for select, radio, checkbox)
            if (['select', 'radio', 'checkbox'].includes(fieldType)) {
                optionsContainer.style.display = 'block';
            } else {
                optionsContainer.style.display = 'none';
            }

            // Range container (for range slider)
            const rangeContainer = document.getElementById('rangeContainer');
            if (fieldType === 'range') {
                rangeContainer.style.display = 'block';
            } else {
                rangeContainer.style.display = 'none';
            }

            // File container (for file upload)
            const fileContainer = document.getElementById('fileContainer');
            if (fieldType === 'file') {
                fileContainer.style.display = 'block';
            } else {
                fileContainer.style.display = 'none';
            }

            // Reset custom accept container
            document.getElementById('customAcceptContainer').style.display = 'none';
            document.getElementById('field_accept').value = '';
        }

        // Add a condition row to the conditions list
        function addConditionRow() {
            const conditionsList = document.getElementById('conditionsList');
            const conditionId = 'condition_' + Date.now();

            // Get all field names for the dropdown
            const fieldOptions = formFields.map((field, index) => {
                return `<option value="${index}">${field.label}</option>`;
            }).join('');

            const conditionHtml = `
                <div class="condition-row border rounded p-2 mb-2" id="${conditionId}">
                    <div class="row g-2">
                        <div class="col-md-4">
                            <select class="form-control form-control-sm condition-field">
                                <option value="">Select Field</option>
                                ${fieldOptions}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control form-control-sm condition-operator">
                                <option value="equals">equals</option>
                                <option value="not_equals">not equals</option>
                                <option value="contains">contains</option>
                                <option value="not_contains">not contains</option>
                                <option value="greater_than">greater than</option>
                                <option value="less_than">less than</option>
                                <option value="is_empty">is empty</option>
                                <option value="is_not_empty">is not empty</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <input type="text" class="form-control form-control-sm condition-value" placeholder="Value">
                        </div>
                        <div class="col-md-1">
                            <button type="button" class="btn btn-sm btn-danger" onclick="removeCondition('${conditionId}')">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;

            conditionsList.insertAdjacentHTML('beforeend', conditionHtml);
        }

        // Remove a condition row
        function removeCondition(conditionId) {
            document.getElementById(conditionId).remove();
        }

        // Update form preview function
        function updateFormPreview() {
            if (formFields.length === 0) {
                formPreview.innerHTML = '<p class="text-muted text-center">Add fields to see the form preview here.</p>';
                return;
            }

            formPreview.innerHTML = '';

            // Create a row container for the two-column layout
            const rowContainer = document.createElement('div');
            rowContainer.className = 'row';
            formPreview.appendChild(rowContainer);

            // Create left and right columns
            const leftColumn = document.createElement('div');
            leftColumn.className = 'col-md-6 pe-md-2';

            const rightColumn = document.createElement('div');
            rightColumn.className = 'col-md-6 ps-md-2';

            rowContainer.appendChild(leftColumn);
            rowContainer.appendChild(rightColumn);

            // Track which column to add to next
            let currentColumn = leftColumn;

            formFields.forEach((field, index) => {
                const fieldContainer = document.createElement('div');
                fieldContainer.className = 'mb-3 p-2 border rounded position-relative';
                fieldContainer.dataset.index = index;

                // Add conditional logic indicator if present
                if (field.conditions && field.conditions.length > 0) {
                    const conditionBadge = document.createElement('span');
                    conditionBadge.className = 'position-absolute top-0 end-0 badge bg-info m-1';
                    conditionBadge.innerHTML = '<i class="bi bi-code-slash"></i> Conditional';
                    conditionBadge.title = 'This field has conditional display logic';
                    fieldContainer.appendChild(conditionBadge);
                }

                const fieldLabel = document.createElement('label');
                fieldLabel.className = 'form-label';
                fieldLabel.textContent = field.label;
                if (field.required) {
                    const requiredSpan = document.createElement('span');
                    requiredSpan.className = 'text-danger ms-1';
                    requiredSpan.textContent = '*';
                    fieldLabel.appendChild(requiredSpan);
                }
                fieldContainer.appendChild(fieldLabel);

                let fieldElement;

                switch (field.type) {
                    case 'text':
                    case 'email':
                    case 'password':
                    case 'tel':
                    case 'url':
                    case 'number':
                    case 'date':
                    case 'time':
                    case 'datetime-local':
                    case 'color':
                        fieldElement = document.createElement('input');
                        fieldElement.type = field.type;
                        fieldElement.className = 'form-control';
                        if (field.placeholder) fieldElement.placeholder = field.placeholder;
                        break;

                    case 'textarea':
                        fieldElement = document.createElement('textarea');
                        fieldElement.className = 'form-control';
                        fieldElement.rows = 3;
                        if (field.placeholder) fieldElement.placeholder = field.placeholder;
                        break;

                    case 'rich-text':
                        fieldElement = document.createElement('div');
                        fieldElement.innerHTML = `
                            <div class="border p-2 bg-light">
                                <div class="btn-toolbar mb-2" role="toolbar">
                                    <div class="btn-group me-2" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-secondary">B</button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary"><i>I</i></button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary"><u>U</u></button>
                                    </div>
                                    <div class="btn-group me-2" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-secondary">
                                            <i class="bi bi-list-ul"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary">
                                            <i class="bi bi-list-ol"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="form-control" style="min-height: 100px;" contenteditable="true">
                                    ${field.placeholder || 'Enter rich text here...'}
                                </div>
                            </div>
                        `;
                        break;

                    case 'range':
                        fieldElement = document.createElement('div');
                        fieldElement.innerHTML = `
                            <input type="range" class="form-range"
                                min="${field.min || 0}"
                                max="${field.max || 100}"
                                step="${field.step || 1}">
                            <div class="d-flex justify-content-between">
                                <small>${field.min || 0}</small>
                                <small>${field.max || 100}</small>
                            </div>
                        `;
                        break;

                    case 'file':
                        fieldElement = document.createElement('div');
                        fieldElement.innerHTML = `
                            <input type="file" class="form-control"
                                ${field.accept ? `accept="${field.accept}"` : ''}>
                            <div class="form-text small">
                                ${field.accept ? `Accepted file types: ${field.accept}` : 'All file types accepted'}
                            </div>
                        `;
                        break;

                    case 'select':
                        fieldElement = document.createElement('select');
                        fieldElement.className = 'form-control';

                        const defaultOption = document.createElement('option');
                        defaultOption.value = '';
                        defaultOption.textContent = field.placeholder || '-- Select an option --';
                        fieldElement.appendChild(defaultOption);

                        if (field.options) {
                            field.options.forEach(option => {
                                const optionElement = document.createElement('option');
                                optionElement.value = option;
                                optionElement.textContent = option;
                                fieldElement.appendChild(optionElement);
                            });
                        }
                        break;

                    case 'radio':
                    case 'checkbox':
                        fieldElement = document.createElement('div');

                        if (field.options) {
                            field.options.forEach(option => {
                                const wrapper = document.createElement('div');
                                wrapper.className = 'form-check';

                                const input = document.createElement('input');
                                input.type = field.type;
                                input.className = 'form-check-input';
                                input.name = `field_${index}`;
                                input.value = option;

                                const label = document.createElement('label');
                                label.className = 'form-check-label';
                                label.textContent = option;

                                wrapper.appendChild(input);
                                wrapper.appendChild(label);
                                fieldElement.appendChild(wrapper);
                            });
                        }
                        break;
                }

                fieldContainer.appendChild(fieldElement);

                // Add field controls
                const controlsDiv = document.createElement('div');
                controlsDiv.className = 'mt-2 d-flex justify-content-between';

                // Add delete button
                const deleteButton = document.createElement('button');
                deleteButton.type = 'button';
                deleteButton.className = 'btn btn-sm btn-outline-danger';
                deleteButton.innerHTML = '<i class="bi bi-trash"></i> Remove';
                deleteButton.addEventListener('click', function() {
                    const index = parseInt(this.closest('.mb-3').dataset.index);
                    formFields.splice(index, 1);
                    updateFormPreview();
                });
                controlsDiv.appendChild(deleteButton);

                // Add edit button
                const editButton = document.createElement('button');
                editButton.type = 'button';
                editButton.className = 'btn btn-sm btn-outline-primary';
                editButton.innerHTML = '<i class="bi bi-pencil"></i> Edit';
                editButton.addEventListener('click', function() {
                    const index = parseInt(this.closest('.mb-3').dataset.index);
                    // Future enhancement: implement field editing
                    alert('Edit functionality will be implemented in a future update.');
                });
                controlsDiv.appendChild(editButton);

                fieldContainer.appendChild(controlsDiv);

                // Determine which column to add the field to
                if (field.fullWidth) {
                    // Create a full-width container
                    const fullWidthContainer = document.createElement('div');
                    fullWidthContainer.className = 'col-12 mb-3';
                    fullWidthContainer.appendChild(fieldContainer);
                    rowContainer.appendChild(fullWidthContainer);
                } else {
                    // Add to alternating columns
                    currentColumn.appendChild(fieldContainer);
                    // Switch columns for next field
                    currentColumn = (currentColumn === leftColumn) ? rightColumn : leftColumn;
                }
            });
        }

        // Reset form builder function
        function resetFormBuilder() {
            formPropertiesForm.reset();
            document.getElementById('formId').value = '';
            currentFormId = null;
            formFields = [];
            updateFormPreview();

            // Reset all containers
            document.getElementById('optionsContainer').style.display = 'none';
            document.getElementById('rangeContainer').style.display = 'none';
            document.getElementById('fileContainer').style.display = 'none';
            document.getElementById('customAcceptContainer').style.display = 'none';
            document.getElementById('conditionsContainer').style.display = 'none';
            document.getElementById('conditionsList').innerHTML = '';

            // Reset field inputs
            document.getElementById('field_label').value = '';
            document.getElementById('field_placeholder').value = '';
            document.getElementById('field_required').checked = false;
            document.getElementById('field_full_width').checked = false;
            document.getElementById('field_options').value = '';
            document.getElementById('enable_conditions').checked = false;
            document.getElementById('field_min').value = '0';
            document.getElementById('field_max').value = '100';
            document.getElementById('field_step').value = '1';
            document.getElementById('field_accept').value = '';
            document.getElementById('field_custom_accept').value = '';
        }

        // Initialize
        toggleFieldOptions();
    });
</script>
@endpush
