<?php

namespace App\Http\Controllers\ZonalCoordinator;

use App\Http\Controllers\Controller;
use App\Models\ActionPlan;
use App\Models\ScientistFeedback;
use App\Models\StateData;
use App\Models\User;
use App\Models\Visit;
use App\Models\ZonalCoordinator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Show the zonal coordinator dashboard.
     */
    public function index()
    {
        // Get the current user's zonal coordinator record
        $coordinator = ZonalCoordinator::where('user_id', Auth::id())->first();

        if (!$coordinator) {
            return view('zonal-coordinator.dashboard', ['stats' => []]);
        }

        // Get districts assigned to this coordinator
        $districtIds = DB::table('district_zonal_coordinator')
            ->where('zonal_coordinator_id', $coordinator->id)
            ->pluck('district_id');

        $districts = StateData::whereIn('id', $districtIds)->get();

        // Get scientists in these districts
        $scientistEmails = $districts->pluck('scientist')->filter()->unique();
        $scientists = User::whereIn('email', $scientistEmails)->where('role', 'scientist')->get();

        // Get action plans in these districts
        $actionPlans = ActionPlan::whereIn('district_id', $districtIds)->get();

        // Get visits by this coordinator
        $visits = Visit::where('zonal_coordinator_id', $coordinator->id)->get();

        // Get feedback provided by this coordinator
        $feedback = ScientistFeedback::where('created_by', Auth::id())->get();

        // Calculate statistics
        $stats = [
            'districts_count' => $districts->count(),
            'scientists_count' => $scientists->count(),
            'action_plans_count' => $actionPlans->count(),
            'completed_action_plans' => $actionPlans->where('status', 'completed')->count(),
            'visits_count' => $visits->count(),
            'completed_visits' => $visits->where('status', 'completed')->count(),
            'feedback_count' => $feedback->count(),
            'pre_cocoon_districts' => $districts->where('status', 'Pre-Cocoon')->count(),
            'post_cocoon_districts' => $districts->where('status', 'Post-Cocoon')->count(),
        ];

        // Get recent action plans
        $recentActionPlans = ActionPlan::whereIn('district_id', $districtIds)
            ->with(['scientist:id,name,email', 'district:id,state,district'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get recent visits
        $recentVisits = Visit::where('zonal_coordinator_id', $coordinator->id)
            ->with('district:id,state,district')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        return view('zonal-coordinator.dashboard', compact('stats', 'recentActionPlans', 'recentVisits'));
    }
}
