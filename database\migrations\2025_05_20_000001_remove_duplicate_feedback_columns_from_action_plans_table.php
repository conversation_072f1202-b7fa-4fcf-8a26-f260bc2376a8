<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        try {
            // First, copy any data from old columns to new columns
            $this->copyDataToNewColumns();
            
            // Then, remove the old columns
            Schema::table('action_plans', function (Blueprint $table) {
                $table->dropColumn([
                    'event_success_rating',
                    'challenges_faced',
                    'suggestions_for_improvement',
                    'objectives_met',
                    'self_assessed_learning_outcome',
                ]);
            });
            
            Log::info('Successfully removed duplicate feedback columns from action_plans table');
        } catch (\Exception $e) {
            Log::error('Error removing duplicate feedback columns: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Copy data from old columns to new columns
     */
    private function copyDataToNewColumns()
    {
        try {
            // Get all action plans
            $actionPlans = DB::table('action_plans')->get();
            
            foreach ($actionPlans as $plan) {
                $updates = [];
                
                // Only update if the new column is empty and the old column has data
                if (empty($plan->scientist_event_success_rating) && !empty($plan->event_success_rating)) {
                    $updates['scientist_event_success_rating'] = $plan->event_success_rating;
                }
                
                if (empty($plan->scientist_challenges_faced) && !empty($plan->challenges_faced)) {
                    $updates['scientist_challenges_faced'] = $plan->challenges_faced;
                }
                
                if (empty($plan->scientist_suggestions_for_improvement) && !empty($plan->suggestions_for_improvement)) {
                    $updates['scientist_suggestions_for_improvement'] = $plan->suggestions_for_improvement;
                }
                
                if (empty($plan->scientist_objectives_met) && !empty($plan->objectives_met)) {
                    $updates['scientist_objectives_met'] = $plan->objectives_met;
                }
                
                if (empty($plan->scientist_self_assessed_learning_outcome) && !empty($plan->self_assessed_learning_outcome)) {
                    $updates['scientist_self_assessed_learning_outcome'] = $plan->self_assessed_learning_outcome;
                }
                
                // Update the record if there are any changes
                if (!empty($updates)) {
                    DB::table('action_plans')
                        ->where('id', $plan->id)
                        ->update($updates);
                }
            }
            
            Log::info('Successfully copied data from old columns to new columns');
        } catch (\Exception $e) {
            Log::error('Error copying data from old columns to new columns: ' . $e->getMessage());
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        try {
            Schema::table('action_plans', function (Blueprint $table) {
                $table->integer('event_success_rating')->nullable()->after('status')->comment('1-5 stars');
                $table->text('challenges_faced')->nullable()->after('event_success_rating');
                $table->text('suggestions_for_improvement')->nullable()->after('challenges_faced');
                $table->json('objectives_met')->nullable()->after('suggestions_for_improvement');
                $table->text('self_assessed_learning_outcome')->nullable()->after('objectives_met');
            });
            
            Log::info('Successfully restored duplicate feedback columns to action_plans table');
        } catch (\Exception $e) {
            Log::error('Error restoring duplicate feedback columns: ' . $e->getMessage());
            throw $e;
        }
    }
};
