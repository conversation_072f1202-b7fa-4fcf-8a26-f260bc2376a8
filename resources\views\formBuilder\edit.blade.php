@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Edit Form</h5>
                    <a href="{{ route('form-builder') }}" class="btn btn-secondary">Back to List</a>
                </div>

                <div class="card-body">
                    <form id="formBuilderForm">
                        <div class="mb-3">
                            <label for="formName" class="form-label">Form Name</label>
                            <input type="text" class="form-control" id="formName" name="form_name" required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Form Fields</label>
                            <div id="formFields" class="border p-3 rounded">
                                <!-- Form fields will be added here -->
                            </div>
                            <div class="mt-2">
                                <button type="button" class="btn btn-success" onclick="addField('text')">Add Text Field</button>
                                <button type="button" class="btn btn-info" onclick="addField('textarea')">Add Textarea</button>
                                <button type="button" class="btn btn-warning" onclick="addField('select')">Add Select</button>
                                <button type="button" class="btn btn-primary" onclick="addField('checkbox')">Add Checkbox</button>
                                <button type="button" class="btn btn-secondary" onclick="addField('radio')">Add Radio</button>
                            </div>
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">Update Form</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
let fieldCounter = 0;
let formId = new URLSearchParams(window.location.search).get('id');

// Load form data when page loads
window.addEventListener('load', function() {
    if (formId) {
        fetch(`/get-form-builder-edit/${formId}`)
            .then(response => response.json())
            .then(data => {
                document.getElementById('formName').value = data.form_name;
                const formStructure = JSON.parse(data.form_structure);
                formStructure.forEach(field => {
                    addField(field.type, field);
                });
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while loading the form');
            });
    }
});

function addField(type, existingField = null) {
    const fieldId = `field_${fieldCounter++}`;
    let fieldHtml = '';

    switch(type) {
        case 'text':
            fieldHtml = createTextField(fieldId, existingField);
            break;
        case 'textarea':
            fieldHtml = createTextareaField(fieldId, existingField);
            break;
        case 'select':
            fieldHtml = createSelectField(fieldId, existingField);
            break;
        case 'checkbox':
            fieldHtml = createCheckboxField(fieldId, existingField);
            break;
        case 'radio':
            fieldHtml = createRadioField(fieldId, existingField);
            break;
    }

    document.getElementById('formFields').insertAdjacentHTML('beforeend', fieldHtml);
}

function createTextField(id, existingField = null) {
    return `
        <div class="field-container mb-3 p-2 border rounded" id="${id}">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0">Text Field</h6>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeField('${id}')">Remove</button>
            </div>
            <div class="mb-2">
                <input type="text" class="form-control" placeholder="Field Label" value="${existingField?.label || ''}" onchange="updateFieldLabel('${id}', this.value)">
            </div>
            <div class="mb-2">
                <input type="text" class="form-control" placeholder="Placeholder" value="${existingField?.placeholder || ''}" onchange="updateFieldPlaceholder('${id}', this.value)">
            </div>
            <div class="form-check">
                <input type="checkbox" class="form-check-input" ${existingField?.required ? 'checked' : ''} onchange="updateFieldRequired('${id}', this.checked)">
                <label class="form-check-label">Required</label>
            </div>
        </div>
    `;
}

function createTextareaField(id, existingField = null) {
    return `
        <div class="field-container mb-3 p-2 border rounded" id="${id}">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0">Textarea Field</h6>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeField('${id}')">Remove</button>
            </div>
            <div class="mb-2">
                <input type="text" class="form-control" placeholder="Field Label" value="${existingField?.label || ''}" onchange="updateFieldLabel('${id}', this.value)">
            </div>
            <div class="mb-2">
                <input type="text" class="form-control" placeholder="Placeholder" value="${existingField?.placeholder || ''}" onchange="updateFieldPlaceholder('${id}', this.value)">
            </div>
            <div class="form-check">
                <input type="checkbox" class="form-check-input" ${existingField?.required ? 'checked' : ''} onchange="updateFieldRequired('${id}', this.checked)">
                <label class="form-check-label">Required</label>
            </div>
        </div>
    `;
}

function createSelectField(id, existingField = null) {
    return `
        <div class="field-container mb-3 p-2 border rounded" id="${id}">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0">Select Field</h6>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeField('${id}')">Remove</button>
            </div>
            <div class="mb-2">
                <input type="text" class="form-control" placeholder="Field Label" value="${existingField?.label || ''}" onchange="updateFieldLabel('${id}', this.value)">
            </div>
            <div class="mb-2">
                <textarea class="form-control" placeholder="Options (one per line)" onchange="updateFieldOptions('${id}', this.value)">${existingField?.options?.join('\n') || ''}</textarea>
            </div>
            <div class="form-check">
                <input type="checkbox" class="form-check-input" ${existingField?.required ? 'checked' : ''} onchange="updateFieldRequired('${id}', this.checked)">
                <label class="form-check-label">Required</label>
            </div>
        </div>
    `;
}

function createCheckboxField(id, existingField = null) {
    const optionsValue = existingField?.options ? existingField.options.join(', ') : '';
    return `
        <div class="field-container mb-3 p-2 border rounded" id="${id}">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0">Checkbox Field</h6>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeField('${id}')">Remove</button>
            </div>
            <div class="mb-2">
                <input type="text" class="form-control" placeholder="Field Label" value="${existingField?.label || ''}" onchange="updateFieldLabel('${id}', this.value)">
            </div>
            <div class="mb-2">
                <input type="text" class="form-control" placeholder="Checkbox Label (for single checkbox)" value="${existingField?.checkboxLabel || ''}" onchange="updateFieldCheckboxLabel('${id}', this.value)">
            </div>
            <div class="mb-2">
                <input type="text" class="form-control" placeholder="Options (comma-separated, leave empty for single checkbox)" value="${optionsValue}" onchange="updateFieldOptions('${id}', this.value)">
                <small class="form-text text-muted">Enter options separated by commas (e.g., Option 1, Option 2, Option 3). Leave empty for a single checkbox.</small>
            </div>
        </div>
    `;
}

function createRadioField(id, existingField = null) {
    return `
        <div class="field-container mb-3 p-2 border rounded" id="${id}">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0">Radio Field</h6>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeField('${id}')">Remove</button>
            </div>
            <div class="mb-2">
                <input type="text" class="form-control" placeholder="Field Label" value="${existingField?.label || ''}" onchange="updateFieldLabel('${id}', this.value)">
            </div>
            <div class="mb-2">
                <textarea class="form-control" placeholder="Options (one per line)" onchange="updateFieldOptions('${id}', this.value)">${existingField?.options?.join('\n') || ''}</textarea>
            </div>
            <div class="form-check">
                <input type="checkbox" class="form-check-input" ${existingField?.required ? 'checked' : ''} onchange="updateFieldRequired('${id}', this.checked)">
                <label class="form-check-label">Required</label>
            </div>
        </div>
    `;
}

function removeField(id) {
    document.getElementById(id).remove();
}

function updateFieldLabel(id, value) {
    const field = document.getElementById(id);
    field.dataset.label = value;
}

function updateFieldPlaceholder(id, value) {
    const field = document.getElementById(id);
    field.dataset.placeholder = value;
}

function updateFieldRequired(id, value) {
    const field = document.getElementById(id);
    field.dataset.required = value;
}

function updateFieldOptions(id, value) {
    const field = document.getElementById(id);
    field.dataset.options = value;
}

function updateFieldCheckboxLabel(id, value) {
    const field = document.getElementById(id);
    field.dataset.checkboxLabel = value;
}

document.getElementById('formBuilderForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formName = document.getElementById('formName').value;
    const fields = [];

    document.querySelectorAll('.field-container').forEach(field => {
        const fieldData = {
            type: field.querySelector('h6').textContent.toLowerCase().replace(' field', ''),
            label: field.dataset.label || '',
            required: field.dataset.required === 'true',
        };

        if (field.dataset.placeholder) {
            fieldData.placeholder = field.dataset.placeholder;
        }

        if (field.dataset.options) {
            fieldData.options = field.dataset.options.split('\n').filter(opt => opt.trim());
        }

        if (field.dataset.checkboxLabel) {
            fieldData.checkboxLabel = field.dataset.checkboxLabel;
        }

        fields.push(fieldData);
    });

    const formData = {
        form_name: formName,
        form_structure: JSON.stringify(fields)
    };

    fetch(`/update-form-builder/${formId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'Accept': 'application/json'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            alert(data.message);
            window.location.href = '/form-builder';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the form');
    });
});
</script>
@endpush
@endsection
