<?php

namespace App\Http\Controllers\ZonalCoordinator;

use App\Http\Controllers\Controller;
use App\Exports\ParticipantFeedbackExport;
use App\Exports\ParticipantFeedbackPdfExport;
use App\Imports\ActionPlanFeedbackImport;
use App\Models\ZonalCoordinator;
use App\Models\ActionPlan;
use App\Models\UserFeedback;
use App\Exports\UserFeedbackTemplateExport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Barryvdh\DomPDF\Facade\Pdf;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class FeedbackImportController extends Controller
{
    /**
     * Display the feedback import page.
     */
    public function index()
    {
        return view('zonal-coordinator.feedback-import.index');
    }

    /**
     * Get all action plans in the coordinator's districts.
     */
    public function getEvents()
    {
        try {
            Log::info('Getting action plans for user ID: ' . Auth::id());

            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', Auth::id())->first();

            if (!$coordinator) {
                Log::warning('Zonal coordinator record not found for user ID: ' . Auth::id());
                return response()->json(['error' => 'Zonal coordinator record not found. Please contact support.'], 404);
            }

            Log::info('Found zonal coordinator ID: ' . $coordinator->id);

            // Get districts assigned to this coordinator
            $districtIds = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->pluck('district_id');

            Log::info('Found district IDs: ' . implode(', ', $districtIds->toArray()));

            if ($districtIds->isEmpty()) {
                Log::warning('No districts assigned to zonal coordinator ID: ' . $coordinator->id);
                return response()->json(['error' => 'No districts are assigned to your account. Please contact support.'], 404);
            }

            // Get all action plans in these districts (not just completed ones)
            $actionPlans = ActionPlan::whereIn('district_id', $districtIds)
                ->with([
                    'scientist:id,name,email',
                    'district:id,state,district'
                ])
                ->select([
                    'id',
                    'title',
                    'planned_date',
                    'location',
                    'status',
                    'type',
                    'district_id',
                    'scientist_id',
                    'created_at',
                    'updated_at'
                ])
                ->orderBy('planned_date', 'desc')
                ->get();

            Log::info('Found ' . $actionPlans->count() . ' action plans');

            if ($actionPlans->isEmpty()) {
                Log::info('No action plans found for districts: ' . implode(', ', $districtIds->toArray()));
                return response()->json([]);
            }

            // Map the plans and handle any potential errors
            $actionPlans = $actionPlans->map(function ($plan) {
                try {
                    // Format the data for display
                    $formattedDate = null;
                    if ($plan->planned_date) {
                        try {
                            $formattedDate = \Carbon\Carbon::parse($plan->planned_date)->format('Y-m-d H:i');
                        } catch (\Exception $e) {
                            Log::error('Error formatting date for plan ' . $plan->id . ': ' . $e->getMessage());
                            $formattedDate = 'Invalid Date';
                        }
                    }

                    // Count existing feedback from user_feedback table
                    $feedbackCount = UserFeedback::where('event_id', $plan->id)->count();

                    return [
                        'id' => $plan->id,
                        'title' => $plan->title ?? 'N/A',
                        'planned_date' => $formattedDate ?? 'N/A',
                        'location' => $plan->location ?? 'N/A',
                        'status' => $plan->status ?? 'N/A',
                        'type' => $plan->type ?? 'N/A',
                        'scientist' => $plan->scientist ? $plan->scientist->name : 'N/A',
                        'district' => $plan->district ? $plan->district->district : 'N/A',
                        'feedback_count' => $feedbackCount,
                        'can_add_review' => $this->canAddReview($plan)
                    ];
                } catch (\Exception $e) {
                    Log::error('Error processing action plan ' . $plan->id . ': ' . $e->getMessage());
                    Log::error('Stack trace: ' . $e->getTraceAsString());
                    return [
                        'id' => $plan->id,
                        'title' => 'Error processing plan',
                        'planned_date' => 'N/A',
                        'location' => 'N/A',
                        'status' => 'error',
                        'type' => 'N/A',
                        'scientist' => 'N/A',
                        'district' => 'N/A',
                        'feedback_count' => 0,
                        'can_add_review' => false
                    ];
                }
            });

            return response()->json($actionPlans);
        } catch (\Exception $e) {
            Log::error('Error getting action plans: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return response()->json(['error' => 'An unexpected error occurred. Please try again later.'], 500);
        }
    }

    /**
     * Check if an action plan is within the 2-day window for adding reviews
     */
    private function canAddReview($actionPlan)
    {
        try {
            if (!$actionPlan->planned_date) {
                return false;
            }

            // Ensure we have a Carbon instance
            $plannedDate = $actionPlan->planned_date instanceof \Carbon\Carbon
                ? $actionPlan->planned_date
                : \Carbon\Carbon::parse($actionPlan->planned_date);

            // For testing purposes, allow reviews for action plans within 30 days
            // TODO: Change back to 2 days in production
            $thirtyDaysAgo = now()->subDays(2);
            return $plannedDate->greaterThanOrEqualTo($thirtyDaysAgo);
        } catch (\Exception $e) {
            Log::error('Error in canAddReview for plan ' . $actionPlan->id . ': ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Get feedback for a specific event.
     */
    public function getEventFeedback($eventId)
    {
        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', Auth::id())->first();

            if (!$coordinator) {
                return view('zonal-coordinator.feedback-import.error', [
                    'message' => 'Zonal coordinator record not found. Please contact support.'
                ]);
            }

            // Get districts assigned to this coordinator
            $districtIds = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->pluck('district_id');

            // Verify the action plan is in one of the coordinator's districts
            $actionPlan = ActionPlan::where('id', $eventId)
                ->whereIn('district_id', $districtIds)
                ->with(['scientist:id,name,email', 'district:id,state,district'])
                ->first();

            if (!$actionPlan) {
                return view('zonal-coordinator.feedback-import.error', [
                    'message' => 'Action plan not found or not in your assigned districts.'
                ]);
            }

            // Get feedback from user_feedback table (our new simplified feedback)
            $feedback = UserFeedback::where('event_id', $eventId)
                ->orderBy('created_at', 'desc')
                ->get();

            // Calculate statistics
            $stats = [
                'total_feedback' => $feedback->count(),
                'avg_benefit_rating' => round($feedback->avg('benefit_rating') ?? 0, 1),
                'avg_speaker_rating' => round($feedback->avg('speaker_rating') ?? 0, 1),
                'recommend_percentage' => $feedback->count() > 0 ?
                    round(($feedback->where('would_recommend', 1)->count() / $feedback->count()) * 100, 1) : 0,
                'total_responses' => $feedback->count(),
            ];

            // Return a proper view instead of JSON
            return view('zonal-coordinator.feedback-import.feedback-details', [
                'actionPlan' => $actionPlan,
                'feedback' => $feedback,
                'stats' => $stats,
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting action plan feedback: ' . $e->getMessage());
            return view('zonal-coordinator.feedback-import.error', [
                'message' => 'An error occurred while loading feedback. Please try again.'
            ]);
        }
    }

    /**
     * Download a sample Excel template for user feedback bulk upload.
     */
    public function downloadTemplate()
    {
        Log::info('FeedbackImportController: Template download method called');

        // Simple test response
        return response()->json([
            'message' => 'Template download method working',
            'timestamp' => now(),
            'method' => 'FeedbackImportController@downloadTemplate'
        ]);
    }

    /**
     * Import user feedback from Excel file.
     */
    public function importFeedback(Request $request)
    {
        $request->validate([
            'event_id' => 'required|exists:action_plans,id',
            'excel_file' => 'required|file|mimes:xlsx,xls,csv|max:10240',
        ]);

        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', Auth::id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'Zonal coordinator record not found'], 404);
            }

            // Get districts assigned to this coordinator
            $districtIds = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->pluck('district_id');

            // Verify the action plan is in one of the coordinator's districts
            $actionPlan = ActionPlan::where('id', $request->event_id)
                ->whereIn('district_id', $districtIds)
                ->first();

            if (!$actionPlan) {
                return response()->json(['error' => 'Action plan not found or not in your districts'], 404);
            }

            // Read the file and process it (supports both Excel and CSV)
            $file = $request->file('excel_file');
            $extension = $file->getClientOriginalExtension();

            if ($extension === 'csv') {
                // Handle CSV file
                $rows = [];
                if (($handle = fopen($file->getPathname(), 'r')) !== FALSE) {
                    while (($data = fgetcsv($handle, 1000, ',')) !== FALSE) {
                        $rows[] = $data;
                    }
                    fclose($handle);
                }
            } else {
                // Handle Excel file
                $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($file);
                $worksheet = $spreadsheet->getActiveSheet();
                $rows = $worksheet->toArray();
            }

            // Remove header row
            array_shift($rows);

            $importedCount = 0;
            $errors = [];

            foreach ($rows as $index => $row) {
                $rowNumber = $index + 2; // +2 because we removed header and Excel starts from 1

                // Skip empty rows
                if (empty(array_filter($row))) {
                    continue;
                }

                try {
                    // Map Excel columns to database fields
                    $name = trim($row[0] ?? '');
                    $phoneNumber = trim($row[1] ?? '');
                    $benefitRating = trim($row[2] ?? '');
                    $wouldRecommend = trim($row[3] ?? '');
                    $mostHelpfulTopic = trim($row[4] ?? '');
                    $speakerRating = trim($row[5] ?? '');
                    $suggestions = trim($row[6] ?? '');

                    // Validate required fields
                    if (empty($name)) {
                        $errors[] = "Row {$rowNumber}: Participant Name is required";
                        continue;
                    }

                    if (empty($phoneNumber)) {
                        $errors[] = "Row {$rowNumber}: Phone Number is required";
                        continue;
                    }

                    // Validate phone number format (basic validation)
                    if (!preg_match('/^[0-9]{10}$/', $phoneNumber)) {
                        $errors[] = "Row {$rowNumber}: Phone Number must be exactly 10 digits";
                        continue;
                    }

                    // Check for duplicate phone number in this action plan
                    $existingFeedback = \App\Models\UserFeedback::where('phone_number', $phoneNumber)
                        ->where('event_id', $request->event_id)
                        ->first();

                    if ($existingFeedback) {
                        $errors[] = "Row {$rowNumber}: Phone Number {$phoneNumber} already exists for this event";
                        continue;
                    }

                    if (empty($benefitRating) || !is_numeric($benefitRating) || $benefitRating < 1 || $benefitRating > 10) {
                        $errors[] = "Row {$rowNumber}: Benefit Rating must be a number between 1 and 10";
                        continue;
                    }

                    if (empty($speakerRating) || !is_numeric($speakerRating) || $speakerRating < 1 || $speakerRating > 5) {
                        $errors[] = "Row {$rowNumber}: Speaker Rating must be a number between 1 and 5";
                        continue;
                    }

                    // Convert would_recommend to boolean
                    $wouldRecommendBool = null;
                    if (strtolower($wouldRecommend) === 'yes' || $wouldRecommend === '1') {
                        $wouldRecommendBool = 1;
                    } elseif (strtolower($wouldRecommend) === 'no' || $wouldRecommend === '0') {
                        $wouldRecommendBool = 0;
                    } else {
                        $errors[] = "Row {$rowNumber}: Would Recommend must be 'Yes' or 'No'";
                        continue;
                    }

                    // Create user feedback record
                    UserFeedback::create([
                        'event_id' => $request->event_id, // This stores the action plan ID
                        'name' => $name, // Required field
                        'phone_number' => $phoneNumber, // Required field
                        'benefit_rating' => (int)$benefitRating,
                        'would_recommend' => $wouldRecommendBool,
                        'most_helpful_topic' => $mostHelpfulTopic ?: null,
                        'speaker_rating' => (int)$speakerRating,
                    ]);

                    // Log suggestions separately if provided (since not stored in DB)
                    if (!empty($suggestions)) {
                        Log::info('Bulk upload suggestions for action plan ' . $request->event_id, [
                            'row' => $rowNumber,
                            'suggestions' => $suggestions,
                            'participant' => $name ?: 'Anonymous'
                        ]);
                    }

                    $importedCount++;

                } catch (\Exception $e) {
                    $errors[] = "Row {$rowNumber}: " . $e->getMessage();
                }
            }

            $message = "Successfully imported {$importedCount} feedback records.";
            if (!empty($errors)) {
                $message .= " " . count($errors) . " rows had errors.";
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'imported_count' => $importedCount,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            Log::error('Error importing user feedback: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to import feedback: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate a QR code for the feedback form.
     */
    public function generateQrCode($eventId)
    {
        try {
            Log::info('QR Code generation requested for event ID: ' . $eventId);
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', Auth::id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'Zonal coordinator record not found'], 404);
            }

            // Get districts assigned to this coordinator
            $districtIds = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->pluck('district_id');

            // Verify the action plan is in one of the coordinator's districts
            $actionPlan = ActionPlan::where('id', $eventId)
                ->whereIn('district_id', $districtIds)
                ->first();

            if (!$actionPlan) {
                return response()->json(['error' => 'Action plan not found or not in your districts'], 404);
            }

            // Check if the action plan is within the 2-day window
            if (!$this->canAddReview($actionPlan)) {
                return response()->json(['error' => 'This action plan is no longer accepting reviews (2 days have passed)'], 403);
            }

            // Generate the feedback URL
            $feedbackUrl = route('public.feedback.actionplan.form', ['actionPlanId' => $eventId]);

            // Generate QR code using online service (fallback for when imagick is not available)
            $qrCodeUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=' . urlencode($feedbackUrl);

            return response()->json([
                'success' => true,
                'action_plan' => $actionPlan,
                'feedback_url' => $feedbackUrl,
                'qr_code_url' => $qrCodeUrl
            ]);
        } catch (\Exception $e) {
            Log::error('Error generating QR code: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to generate QR code'], 500);
        }
    }

    /**
     * Export feedback data to Excel.
     */
    public function exportExcel(Request $request)
    {
        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', Auth::id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'Zonal coordinator record not found'], 404);
            }

            // Get districts assigned to this coordinator
            $districtIds = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->pluck('district_id');

            // Prepare export parameters
            $params = [
                'event_id' => $request->input('event_id'),
                'start_date' => $request->input('start_date'),
                'end_date' => $request->input('end_date'),
                'district_ids' => $districtIds->toArray(),
            ];

            // Generate filename
            $filename = 'action_plan_feedback_';
            if ($request->input('event_id')) {
                $actionPlan = ActionPlan::find($request->input('event_id'));
                if ($actionPlan) {
                    $filename .= strtolower(str_replace(' ', '_', $actionPlan->title)) . '_';
                }
            }
            $filename .= date('Y-m-d') . '.xlsx';

            return Excel::download(new ParticipantFeedbackExport($params), $filename);
        } catch (\Exception $e) {
            Log::error('Error exporting feedback to Excel: ' . $e->getMessage());
            return back()->with('error', 'Failed to export feedback: ' . $e->getMessage());
        }
    }

    /**
     * Export feedback data to PDF.
     */
    public function exportPdf(Request $request)
    {
        try {
            // Get the current user's zonal coordinator record
            $coordinator = ZonalCoordinator::where('user_id', Auth::id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'Zonal coordinator record not found'], 404);
            }

            // Get districts assigned to this coordinator
            $districtIds = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->pluck('district_id');

            // Prepare export parameters
            $params = [
                'event_id' => $request->input('event_id'),
                'start_date' => $request->input('start_date'),
                'end_date' => $request->input('end_date'),
                'district_ids' => $districtIds->toArray(),
                'include_analytics' => $request->input('include_analytics', true),
            ];

            // Generate filename
            $filename = 'action_plan_feedback_';
            if ($request->input('event_id')) {
                $actionPlan = ActionPlan::find($request->input('event_id'));
                if ($actionPlan) {
                    $filename .= strtolower(str_replace(' ', '_', $actionPlan->title)) . '_';
                }
            }
            $filename .= date('Y-m-d') . '.pdf';

            // Create PDF export
            $export = new ParticipantFeedbackPdfExport($params);
            $pdf = PDF::loadView('exports.participant-feedback-pdf', $export->view()->getData());

            return $pdf->download($filename);
        } catch (\Exception $e) {
            Log::error('Error exporting feedback to PDF: ' . $e->getMessage());
            return back()->with('error', 'Failed to export feedback: ' . $e->getMessage());
        }
    }

    public function getActionPlans()
    {
        try {
            $coordinator = ZonalCoordinator::where('user_id', Auth::id())->first();

            if (!$coordinator) {
                return response()->json(['error' => 'Zonal coordinator record not found'], 404);
            }

            $districtIds = DB::table('district_zonal_coordinator')
                ->where('zonal_coordinator_id', $coordinator->id)
                ->pluck('district_id');

            $actionPlans = ActionPlan::whereIn('district_id', $districtIds)
                ->select('id', 'title', 'location', 'planned_date', 'status')
                ->orderBy('planned_date', 'desc')
                ->get();

            return response()->json($actionPlans);
        } catch (\Exception $e) {
            Log::error('Error getting action plans: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get action plans'], 500);
        }
    }
}
