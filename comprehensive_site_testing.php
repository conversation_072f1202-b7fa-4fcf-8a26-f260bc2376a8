<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\FormBuilder;
use App\Models\SwotAnalysis;
use App\Models\ActionPlan;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

echo "=== COMPREHENSIVE SITE TESTING ===\n\n";

// Initialize Laravel app
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

class ComprehensiveTester
{
    private $testResults = [];
    private $testUsers = [];
    
    public function runAllTests()
    {
        echo "🚀 Starting Comprehensive Site Testing...\n";
        echo "📍 Application URL: http://localhost:8000\n";
        echo "📊 Database: " . config('database.connections.mysql.database') . "\n\n";
        
        $this->testDatabaseConnection();
        $this->testUserAuthentication();
        $this->testUserRoles();
        $this->testFormBuilderSystem();
        $this->testSwotAnalysisSystem();
        $this->testActionPlanSystem();
        $this->testFileUploadSecurity();
        $this->testDashboardFunctionality();
        $this->testSecurityFeatures();
        
        $this->printFinalSummary();
        $this->cleanup();
    }
    
    public function testDatabaseConnection()
    {
        echo "🧪 Testing Database Connection...\n";
        
        try {
            $connection = DB::connection();
            $connection->getPdo();
            
            echo "   ✅ Database connection successful\n";
            echo "   📊 Database: " . $connection->getDatabaseName() . "\n";
            
            // Test basic queries
            $userCount = User::count();
            $formCount = FormBuilder::count();
            
            echo "   📊 Users in database: {$userCount}\n";
            echo "   📊 Forms in database: {$formCount}\n";
            
            $this->testResults['database_connection'] = true;
            
        } catch (Exception $e) {
            echo "   ❌ Database connection failed: " . $e->getMessage() . "\n";
            $this->testResults['database_connection'] = false;
        }
        
        echo "\n";
    }
    
    public function testUserAuthentication()
    {
        echo "🧪 Testing User Authentication System...\n";
        
        try {
            // Test user creation
            $testUser = $this->createTestUser('scientist');
            
            if ($testUser) {
                echo "   ✅ User creation successful\n";
                
                // Test password hashing
                if (Hash::check('testpassword123', $testUser->password)) {
                    echo "   ✅ Password hashing and verification working\n";
                } else {
                    echo "   ❌ Password hashing/verification failed\n";
                }
                
                // Test user retrieval
                $retrievedUser = User::where('email', $testUser->email)->first();
                if ($retrievedUser && $retrievedUser->id === $testUser->id) {
                    echo "   ✅ User retrieval successful\n";
                } else {
                    echo "   ❌ User retrieval failed\n";
                }
                
                $this->testResults['user_authentication'] = true;
            } else {
                echo "   ❌ User creation failed\n";
                $this->testResults['user_authentication'] = false;
            }
            
        } catch (Exception $e) {
            echo "   ❌ Authentication test exception: " . $e->getMessage() . "\n";
            $this->testResults['user_authentication'] = false;
        }
        
        echo "\n";
    }
    
    public function testUserRoles()
    {
        echo "🧪 Testing User Role System...\n";
        
        $roles = ['super_admin', 'admin', 'scientist', 'zonal_coordinator', 'district_state_coordinator', 'user'];
        $successCount = 0;
        
        foreach ($roles as $role) {
            try {
                $user = $this->createTestUser($role);
                
                if ($user && $user->role === $role) {
                    echo "   ✅ Role '{$role}' assigned successfully\n";
                    $successCount++;
                } else {
                    echo "   ❌ Role '{$role}' assignment failed\n";
                }
                
            } catch (Exception $e) {
                echo "   ❌ Role '{$role}' test exception: " . $e->getMessage() . "\n";
            }
        }
        
        $this->testResults['user_roles'] = $successCount === count($roles);
        echo "   📊 Roles tested: {$successCount}/" . count($roles) . "\n\n";
    }
    
    public function testFormBuilderSystem()
    {
        echo "🧪 Testing Form Builder System...\n";
        
        try {
            // Create a test form
            $formData = [
                'form_name' => 'Comprehensive Test Form ' . time(),
                'target_user' => 'all_scientists',
                'form_structure' => json_encode([
                    [
                        'type' => 'text',
                        'label' => 'Full Name',
                        'required' => true,
                        'placeholder' => 'Enter your full name'
                    ],
                    [
                        'type' => 'email',
                        'label' => 'Email Address',
                        'required' => true,
                        'placeholder' => 'Enter your email'
                    ],
                    [
                        'type' => 'select',
                        'label' => 'Department',
                        'required' => true,
                        'options' => ['Research', 'Development', 'Quality Control']
                    ],
                    [
                        'type' => 'file',
                        'label' => 'Upload Document',
                        'required' => false,
                        'accept' => '.pdf,.doc,.docx'
                    ]
                ]),
                'description' => 'Test form for comprehensive testing',
                'deadline' => now()->addDays(30)->format('Y-m-d'),
                'created_by' => 1
            ];
            
            $form = FormBuilder::create($formData);
            
            if ($form) {
                echo "   ✅ Form creation successful\n";
                echo "   📊 Form ID: {$form->id}\n";
                echo "   📊 Form Name: {$form->form_name}\n";
                
                // Test form structure parsing
                $structure = is_string($form->form_structure)
                    ? json_decode($form->form_structure, true)
                    : $form->form_structure;

                if (is_array($structure) && count($structure) === 4) {
                    echo "   ✅ Form structure parsing successful\n";
                } else {
                    echo "   ❌ Form structure parsing failed\n";
                }
                
                // Test form retrieval
                $retrievedForm = FormBuilder::find($form->id);
                if ($retrievedForm) {
                    echo "   ✅ Form retrieval successful\n";
                } else {
                    echo "   ❌ Form retrieval failed\n";
                }
                
                $this->testResults['form_builder'] = true;
            } else {
                echo "   ❌ Form creation failed\n";
                $this->testResults['form_builder'] = false;
            }
            
        } catch (Exception $e) {
            echo "   ❌ Form builder test exception: " . $e->getMessage() . "\n";
            $this->testResults['form_builder'] = false;
        }
        
        echo "\n";
    }
    
    public function testSwotAnalysisSystem()
    {
        echo "🧪 Testing SWOT Analysis System...\n";
        
        try {
            $swotData = [
                'scientist_id' => $this->getTestUserId('scientist'),
                'district_id' => 1, // Use a valid district ID
                'strengths' => 'Strong research capabilities, experienced team',
                'weaknesses' => 'Limited funding, outdated equipment',
                'opportunities' => 'Government support, market demand',
                'threats' => 'Competition, regulatory changes'
            ];
            
            $swot = SwotAnalysis::create($swotData);
            
            if ($swot) {
                echo "   ✅ SWOT analysis creation successful\n";
                echo "   📊 SWOT ID: {$swot->id}\n";
                echo "   📊 District: {$swot->district}\n";
                
                // Test SWOT retrieval
                $retrievedSwot = SwotAnalysis::find($swot->id);
                if ($retrievedSwot) {
                    echo "   ✅ SWOT analysis retrieval successful\n";
                } else {
                    echo "   ❌ SWOT analysis retrieval failed\n";
                }
                
                $this->testResults['swot_analysis'] = true;
            } else {
                echo "   ❌ SWOT analysis creation failed\n";
                $this->testResults['swot_analysis'] = false;
            }
            
        } catch (Exception $e) {
            echo "   ❌ SWOT analysis test exception: " . $e->getMessage() . "\n";
            $this->testResults['swot_analysis'] = false;
        }
        
        echo "\n";
    }
    
    public function testActionPlanSystem()
    {
        echo "🧪 Testing Action Plan System...\n";
        
        try {
            $actionPlanData = [
                'scientist_id' => $this->getTestUserId('scientist'),
                'district_id' => 1, // Use a valid district ID
                'type' => 'training',
                'title' => 'Test Action Plan ' . time(),
                'location' => 'Test Location',
                'planned_date' => now()->addDays(7)->format('Y-m-d H:i:s'),
                'expected_participants' => 25,
                'status' => 'planned'
            ];
            
            $actionPlan = ActionPlan::create($actionPlanData);
            
            if ($actionPlan) {
                echo "   ✅ Action plan creation successful\n";
                echo "   📊 Action Plan ID: {$actionPlan->id}\n";
                echo "   📊 Title: {$actionPlan->title}\n";
                echo "   📊 Status: {$actionPlan->status}\n";
                
                // Test action plan retrieval
                $retrievedPlan = ActionPlan::find($actionPlan->id);
                if ($retrievedPlan) {
                    echo "   ✅ Action plan retrieval successful\n";
                } else {
                    echo "   ❌ Action plan retrieval failed\n";
                }
                
                $this->testResults['action_plan'] = true;
            } else {
                echo "   ❌ Action plan creation failed\n";
                $this->testResults['action_plan'] = false;
            }
            
        } catch (Exception $e) {
            echo "   ❌ Action plan test exception: " . $e->getMessage() . "\n";
            $this->testResults['action_plan'] = false;
        }
        
        echo "\n";
    }
    
    public function testFileUploadSecurity()
    {
        echo "🧪 Testing File Upload Security...\n";
        
        try {
            // Test file extension validation
            $dangerousExtensions = ['php', 'exe', 'bat', 'scr', 'js'];
            $safeExtensions = ['pdf', 'jpg', 'png', 'docx', 'xlsx'];
            
            $securityScore = 0;
            $totalTests = count($dangerousExtensions) + count($safeExtensions);
            
            foreach ($dangerousExtensions as $ext) {
                if (in_array($ext, ['php', 'exe', 'bat', 'scr', 'js'])) {
                    echo "   ✅ Dangerous extension '{$ext}' properly identified\n";
                    $securityScore++;
                }
            }
            
            foreach ($safeExtensions as $ext) {
                if (in_array($ext, ['pdf', 'jpg', 'png', 'docx', 'xlsx', 'txt'])) {
                    echo "   ✅ Safe extension '{$ext}' properly allowed\n";
                    $securityScore++;
                }
            }
            
            $this->testResults['file_upload_security'] = $securityScore >= ($totalTests * 0.8);
            echo "   📊 Security score: {$securityScore}/{$totalTests}\n";
            
        } catch (Exception $e) {
            echo "   ❌ File upload security test exception: " . $e->getMessage() . "\n";
            $this->testResults['file_upload_security'] = false;
        }
        
        echo "\n";
    }
    
    public function testDashboardFunctionality()
    {
        echo "🧪 Testing Dashboard Functionality...\n";
        
        try {
            // Test data aggregation for dashboard
            $userCount = User::count();
            $formCount = FormBuilder::count();
            $swotCount = SwotAnalysis::count();
            $actionPlanCount = ActionPlan::count();
            
            echo "   📊 Dashboard Data:\n";
            echo "      Users: {$userCount}\n";
            echo "      Forms: {$formCount}\n";
            echo "      SWOT Analyses: {$swotCount}\n";
            echo "      Action Plans: {$actionPlanCount}\n";
            
            if ($userCount >= 0 && $formCount >= 0 && $swotCount >= 0 && $actionPlanCount >= 0) {
                echo "   ✅ Dashboard data aggregation successful\n";
                $this->testResults['dashboard_functionality'] = true;
            } else {
                echo "   ❌ Dashboard data aggregation failed\n";
                $this->testResults['dashboard_functionality'] = false;
            }
            
        } catch (Exception $e) {
            echo "   ❌ Dashboard functionality test exception: " . $e->getMessage() . "\n";
            $this->testResults['dashboard_functionality'] = false;
        }
        
        echo "\n";
    }
    
    public function testSecurityFeatures()
    {
        echo "🧪 Testing Security Features...\n";
        
        try {
            // Test password hashing
            $password = 'testpassword123';
            $hashedPassword = Hash::make($password);
            
            if (Hash::check($password, $hashedPassword)) {
                echo "   ✅ Password hashing and verification working\n";
            } else {
                echo "   ❌ Password hashing/verification failed\n";
            }
            
            // Test input sanitization (basic check)
            $maliciousInput = '<script>alert("xss")</script>';
            $sanitized = htmlspecialchars($maliciousInput, ENT_QUOTES, 'UTF-8');
            
            if (strpos($sanitized, '<script>') === false) {
                echo "   ✅ Basic input sanitization working\n";
            } else {
                echo "   ❌ Basic input sanitization failed\n";
            }
            
            $this->testResults['security_features'] = true;
            
        } catch (Exception $e) {
            echo "   ❌ Security features test exception: " . $e->getMessage() . "\n";
            $this->testResults['security_features'] = false;
        }
        
        echo "\n";
    }
    
    private function createTestUser($role)
    {
        try {
            $user = User::create([
                'name' => "Test {$role} User " . time(),
                'email' => "test{$role}" . time() . '@example.com',
                'phone_number' => '123456789' . rand(0, 9),
                'designation' => 'Test Designation',
                'password' => Hash::make('testpassword123'),
                'role' => $role,
                'district' => 'Test District',
                'block' => 'Test Block'
            ]);
            
            $this->testUsers[] = $user->id;
            return $user;
            
        } catch (Exception $e) {
            echo "   ❌ Error creating test user: " . $e->getMessage() . "\n";
            return null;
        }
    }
    
    private function getTestUserId($role)
    {
        $user = User::where('role', $role)->first();
        return $user ? $user->id : 1; // Fallback to user ID 1
    }
    
    public function printFinalSummary()
    {
        echo "📊 COMPREHENSIVE TESTING SUMMARY\n";
        echo str_repeat("=", 60) . "\n";
        
        $totalTests = count($this->testResults);
        $passedTests = array_sum($this->testResults);
        
        foreach ($this->testResults as $test => $result) {
            $status = $result ? "✅ PASS" : "❌ FAIL";
            echo sprintf("%-35s %s\n", ucwords(str_replace('_', ' ', $test)), $status);
        }
        
        echo str_repeat("-", 60) . "\n";
        echo sprintf("Total Tests: %d | Passed: %d | Failed: %d\n", 
                    $totalTests, $passedTests, $totalTests - $passedTests);
        
        $percentage = $totalTests > 0 ? round(($passedTests / $totalTests) * 100, 2) : 0;
        echo sprintf("Success Rate: %s%%\n", $percentage);
        
        if ($percentage >= 90) {
            echo "🎉 Excellent! The site is functioning very well!\n";
        } elseif ($percentage >= 75) {
            echo "✅ Good! The site is functioning well with minor issues.\n";
        } elseif ($percentage >= 60) {
            echo "⚠️  The site has some issues that need attention.\n";
        } else {
            echo "🚨 The site has significant issues that require immediate attention!\n";
        }
        
        echo "\n📋 Next Steps:\n";
        echo "   1. Review failed tests and address issues\n";
        echo "   2. Test the web interface manually at http://localhost:8000\n";
        echo "   3. Test user workflows end-to-end\n";
        echo "   4. Perform security testing with real attack vectors\n";
        echo "   5. Test performance under load\n";
    }
    
    public function cleanup()
    {
        echo "\n🧹 Cleaning up test data...\n";
        
        try {
            // Clean up test users
            if (!empty($this->testUsers)) {
                User::whereIn('id', $this->testUsers)->delete();
                echo "   ✅ Test users cleaned up\n";
            }
            
            // Clean up test forms
            FormBuilder::where('form_name', 'like', 'Comprehensive Test Form%')->delete();
            FormBuilder::where('form_name', 'like', 'Test % Form%')->delete();
            echo "   ✅ Test forms cleaned up\n";
            
            // Clean up test SWOT analyses
            SwotAnalysis::where('district', 'Test District')->delete();
            echo "   ✅ Test SWOT analyses cleaned up\n";
            
            // Clean up test action plans
            ActionPlan::where('title', 'like', 'Test Action Plan%')->delete();
            echo "   ✅ Test action plans cleaned up\n";
            
        } catch (Exception $e) {
            echo "   ⚠️  Cleanup warning: " . $e->getMessage() . "\n";
        }
        
        echo "   ✅ Cleanup completed\n";
    }
}

// Run the comprehensive tests
$tester = new ComprehensiveTester();
$tester->runAllTests();

echo "\n=== COMPREHENSIVE TESTING COMPLETE ===\n";
echo "🌐 Visit http://localhost:8000 to test the web interface manually\n";
