#!/bin/bash

echo "Setting up project after PHP 8.2 installation..."

# Install Composer if not available
if ! command -v composer &> /dev/null; then
    echo "Installing Composer..."
    curl -sS https://getcomposer.org/installer | php
    sudo mv composer.phar /usr/local/bin/composer
    sudo chmod +x /usr/local/bin/composer
fi

# Update Composer dependencies
echo "Updating Composer dependencies..."
composer install --no-dev --optimize-autoloader

# Run the migration to add unique constraint
echo "Running migration for unique form names..."
php artisan migrate --path=database/migrations/2025_06_19_000001_add_unique_constraint_to_form_name.php

# Run all pending migrations
echo "Running all pending migrations..."
php artisan migrate

# Clear caches
echo "Clearing caches..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

# Run tests
echo "Running custom form builder tests..."
php artisan test tests/Feature/CustomFormBuilderTest.php --verbose

echo "Setup completed! Your custom form system should now be working."
