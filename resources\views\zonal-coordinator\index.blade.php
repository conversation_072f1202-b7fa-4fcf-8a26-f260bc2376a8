// ... existing code ...

function saveFeedback() {
    // ... existing code ...

    var formData = new FormData(form[0]);
    var objectivesMet = [];
    $('input[name="objectives_met[]"]').each(function() {
        objectivesMet.push($(this).val());
    });

    var jsonData = {
        action_plan_id: formData.get('action_plan_id'),
        scientist_performance_rating: formData.get('scientist_performance_rating'),
        male_participants: formData.get('male_participants'),
        female_participants: formData.get('female_participants'),
        other_participants: formData.get('other_participants'),
        photos: [], // This will be handled separately if needed
        gis_location: formData.get('gis_location'),
        event_success_rating: formData.get('event_success_rating'),
        challenges: formData.get('challenges'),
        suggestions: formData.get('suggestions'),
        objectives_met: objectivesMet, // Use the collected array
        self_assessed_learning_outcome: formData.get('self_assessed_learning_outcome'),
        _token: formData.get('_token')
    };

    // Handle photos separately if they are being uploaded as files
    if (formData.has('photos[]')) {
        formData.getAll('photos[]').forEach(function(photo) {
            jsonData.photos.push(photo);
        });
    }

    $.ajax({
        url: form.attr('action'),
        type: 'POST',
        data: jsonData,
        processData: false,
        contentType: 'application/json', // Change content type to application/json
        success: function(response) {
            // ... existing code ...
        },
        error: function(xhr) {
            // ... existing code ...
        }
    });
}

// ... existing code ...
