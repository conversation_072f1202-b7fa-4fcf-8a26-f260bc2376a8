<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;
use App\Models\FormBuilder;

class FixFormTables extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'forms:fix-tables {--dry-run : Show what would be done without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create missing dynamic tables for forms';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        if ($dryRun) {
            $this->info('🔍 DRY RUN MODE - No changes will be made');
        }
        
        $this->info('🔧 Checking form tables...');
        
        $forms = FormBuilder::all();
        $missingTables = [];
        $existingTables = [];
        
        foreach ($forms as $form) {
            $tableName = 'form_' . Str::slug($form->form_name, '_');
            $exists = Schema::hasTable($tableName);
            
            if ($exists) {
                $existingTables[] = [
                    'form' => $form->form_name,
                    'table' => $tableName
                ];
            } else {
                $missingTables[] = [
                    'form' => $form,
                    'table' => $tableName
                ];
            }
        }
        
        // Show existing tables
        if (!empty($existingTables)) {
            $this->info("\n✅ Existing tables:");
            foreach ($existingTables as $table) {
                $this->line("   • {$table['form']} → {$table['table']}");
            }
        }
        
        // Show missing tables
        if (!empty($missingTables)) {
            $this->warn("\n❌ Missing tables:");
            foreach ($missingTables as $table) {
                $this->line("   • {$table['form']->form_name} → {$table['table']}");
            }
            
            if (!$dryRun) {
                if ($this->confirm("\nCreate missing tables?")) {
                    $this->createMissingTables($missingTables);
                }
            } else {
                $this->info("\n📋 Would create " . count($missingTables) . " missing tables");
            }
        } else {
            $this->info("\n🎉 All form tables exist!");
        }
    }
    
    private function createMissingTables($missingTables)
    {
        $this->info("\n🔨 Creating missing tables...");
        
        foreach ($missingTables as $tableInfo) {
            $form = $tableInfo['form'];
            $tableName = $tableInfo['table'];
            
            try {
                Schema::create($tableName, function ($table) use ($form) {
                    $table->id();
                    $table->unsignedBigInteger('user_id');
                    
                    foreach ($form->form_structure as $field) {
                        $columnName = Str::slug($field['label'], '_');
                        
                        switch ($field['type']) {
                            case 'text':
                            case 'email':
                            case 'url':
                                $table->text($columnName)->nullable();
                                break;
                            case 'number':
                                $table->integer($columnName)->nullable();
                                break;
                            case 'textarea':
                                $table->longText($columnName)->nullable();
                                break;
                            case 'checkbox':
                                $table->boolean($columnName)->default(false);
                                break;
                            case 'radio':
                            case 'select':
                                $table->string($columnName)->nullable();
                                break;
                            case 'date':
                                $table->date($columnName)->nullable();
                                break;
                            case 'file':
                            case 'image':
                                $table->string($columnName)->nullable();
                                break;
                            default:
                                $table->text($columnName)->nullable();
                        }
                    }
                    
                    $table->timestamps();
                    $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
                });
                
                $this->info("   ✅ Created: {$tableName}");
                
            } catch (\Exception $e) {
                $this->error("   ❌ Failed to create {$tableName}: " . $e->getMessage());
            }
        }
        
        $this->info("\n🎉 Table creation complete!");
    }
}
