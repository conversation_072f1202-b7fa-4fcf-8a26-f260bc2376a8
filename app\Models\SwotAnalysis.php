<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SwotAnalysis extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'swot';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'scientist_id',
        'district_id',
        'strengths',
        'weaknesses',
        'opportunities',
        'threats',
    ];

    /**
     * Get the scientist associated with this SWOT analysis.
     */
    public function scientist()
    {
        return $this->belongsTo(User::class, 'scientist_id');
    }

    /**
     * Get the district associated with this SWOT analysis.
     */
    public function district()
    {
        return $this->belongsTo(StateData::class, 'district_id');
    }
}
