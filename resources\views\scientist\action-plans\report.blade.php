@extends('layouts.app')

@section('content')
<style>
    .rating {
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;
        margin-bottom: 1rem;
    }

    .rating input {
        display: none;
    }

    .rating label {
        cursor: pointer;
        width: 30px;
        height: 30px;
        margin: 0 2px;
        background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ddd"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>') no-repeat center center;
        background-size: contain;
        transition: transform 0.2s ease;
    }

    .rating input:checked ~ label,
    .rating label:hover,
    .rating label:hover ~ label {
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffd700"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>');
    }

    .rating label:hover,
    .rating label:hover ~ label {
        transform: scale(1.1);
    }
</style>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Submit Event Report</h5>
                </div>

                <div class="card-body">
                    <form id="reportForm" method="POST" action="{{ route('scientist.action-plans.report.submit', $actionPlan->id) }}">
                        @csrf

                        <div class="mb-3">
                            <label class="form-label">Event Success Rating</label>
                            <div class="rating">
                                <input type="radio" id="success_rating_5" name="scientist_event_success_rating" value="5" required>
                                <label for="success_rating_5" title="5 stars"></label>
                                <input type="radio" id="success_rating_4" name="scientist_event_success_rating" value="4">
                                <label for="success_rating_4" title="4 stars"></label>
                                <input type="radio" id="success_rating_3" name="scientist_event_success_rating" value="3">
                                <label for="success_rating_3" title="3 stars"></label>
                                <input type="radio" id="success_rating_2" name="scientist_event_success_rating" value="2">
                                <label for="success_rating_2" title="2 stars"></label>
                                <input type="radio" id="success_rating_1" name="scientist_event_success_rating" value="1">
                                <label for="success_rating_1" title="1 star"></label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="scientist_challenges_faced" class="form-label">Challenges Faced</label>
                            <textarea class="form-control" id="scientist_challenges_faced" name="scientist_challenges_faced" rows="3" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="scientist_improvement_suggestions" class="form-label">Improvement Suggestions</label>
                            <textarea class="form-control" id="scientist_improvement_suggestions" name="scientist_improvement_suggestions" rows="3" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="scientist_remarks" class="form-label">Additional Remarks</label>
                            <textarea class="form-control" id="scientist_remarks" name="scientist_remarks" rows="3"></textarea>
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">Submit Report</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const reportForm = document.getElementById('reportForm');

        reportForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(reportForm);
            const jsonData = {};

            formData.forEach((value, key) => {
                jsonData[key] = value;
            });

            fetch(reportForm.action, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(jsonData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    if (typeof data.error === 'object') {
                        const errorMessages = Object.values(data.error).flat();
                        alert(errorMessages.join('\n'));
                    } else {
                        alert(data.error);
                    }
                    return;
                }

                alert(data.message || 'Report submitted successfully');
                window.location.href = '{{ route("scientist.action-plans.index") }}';
            })
            .catch(error => {
                console.error('Error submitting report:', error);
                alert('Error submitting report. Please try again later.');
            });
        });
    });
</script>
@endpush

@endsection
