<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'role' => \App\Http\Middleware\CheckRole::class,
            'secure.upload' => \App\Http\Middleware\SecureFileUpload::class,
            'auth.ratelimit' => \App\Http\Middleware\AuthRateLimit::class,
            'sanitize.input' => \App\Http\Middleware\SanitizeInput::class,
        ]);

        // Add global middleware
        $middleware->append(\App\Http\Middleware\SanitizeInput::class);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        // No exceptions configuration needed
    })->create();
