<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class UserFeedbackTemplateExport implements WithMultipleSheets
{
    public function sheets(): array
    {
        return [
            'Template' => new UserFeedbackTemplateSheet(),
            'Instructions' => new UserFeedbackInstructionsSheet(),
        ];
    }
}

class UserFeedbackTemplateSheet implements FromArray, WithHeadings, WithStyles, WithColumnWidths
{
    public function array(): array
    {
        // Return empty array as per user preference - no sample data
        return [];
    }

    public function headings(): array
    {
        return [
            'Participant Name (Required)',
            'Phone Number (Required)',
            'Benefit Rating (1-10)',
            'Would Recommend (Yes/No)',
            'Most Helpful Topic (Optional)',
            'Speaker Rating (1-5)',
            'Suggestions (Optional)'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the header row
            1 => [
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E3F2FD']
                ]
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 25,
            'B' => 20,
            'C' => 20,
            'D' => 20,
            'E' => 30,
            'F' => 20,
            'G' => 35,
        ];
    }
}

class UserFeedbackInstructionsSheet implements FromArray, WithStyles, WithColumnWidths
{
    public function array(): array
    {
        return [
            ['User Feedback Bulk Upload Template'],
            [''],
            ['Instructions:'],
            ['1. Fill in the feedback data in the "Template" tab'],
            ['2. Participant Name and Phone Number are required'],
            ['3. Benefit Rating: Enter a number from 1 to 10'],
            ['4. Would Recommend: Enter "Yes" or "No"'],
            ['5. Most Helpful Topic: Enter text or leave blank'],
            ['6. Speaker Rating: Enter a number from 1 to 5'],
            ['7. Suggestions: Enter text or leave blank'],
            ['8. Do not modify the header row'],
            ['9. Save the file and upload it using the Bulk Upload button'],
            [''],
            ['Note: The event/action plan ID will be automatically'],
            ['added when you upload this file.'],
            [''],
            ['Template Format:'],
            ['The Template sheet contains only headers - add your feedback data below the header row.'],
            ['Follow the column format exactly as shown in the headers.'],
            [''],
            ['Required Fields:'],
            ['- Participant Name'],
            ['- Phone Number'],
            ['- Benefit Rating (must be 1-10)'],
            ['- Would Recommend (must be Yes or No)'],
            ['- Speaker Rating (must be 1-5)'],
            [''],
            ['Optional Fields:'],
            ['- Most Helpful Topic'],
            ['- Suggestions'],
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true, 'size' => 14]],
            3 => ['font' => ['bold' => true]],
            17 => ['font' => ['bold' => true]],
            21 => ['font' => ['bold' => true]],
            25 => ['font' => ['bold' => true]],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 50,
        ];
    }
}
