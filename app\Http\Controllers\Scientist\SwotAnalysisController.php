<?php

namespace App\Http\Controllers\Scientist;

use App\Http\Controllers\Controller;
use App\Models\Swot;
use App\Models\StateData;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class SwotAnalysisController extends Controller
{
    /**
     * Display the SWOT analysis page.
     */
    public function index()
    {
        return view('scientist.swot.new_index');
    }

    /**
     * Handle SWOT request - either display the form or save data if query parameters exist
     */
    public function handleSwotRequest(Request $request)
    {
        // Check if this is a save request with query parameters
        if ($request->has('strengths') || $request->has('weaknesses') ||
            $request->has('opportunities') || $request->has('threats')) {

            Log::info('SWOT Analysis - Detected GET request with query parameters, redirecting to save method');
            return $this->saveSwotAnalysis($request);
        }

        // Otherwise, just show the form
        return $this->index();
    }

    /**
     * Get the scientist's district.
     * If no district is assigned, creates a default one.
     *
     * @return \App\Models\StateData|null
     */
    private function getScientistDistrict()
    {
        $scientistEmail = Auth::user()->email;
        $district = StateData::where('scientist', $scientistEmail)->first();

        // If no district is assigned to this scientist, create a default one
        if (!$district) {
            Log::info('SWOT Analysis - No district found for scientist: ' . $scientistEmail . '. Creating default district.');

            try {
                // Generate a new ID for the state_data record
                $newId = StateData::max('id') ? StateData::max('id') + 1 : 1;

                // Create a new default state_data record
                $district = new StateData();
                $district->id = $newId;
                $district->state = 'Unknown';
                $district->district = 'Unknown';
                $district->status = 'Unknown';
                $district->scientist = $scientistEmail;
                $district->save();

                Log::info('SWOT Analysis - Created default district record with ID: ' . $district->id . ' for scientist: ' . $scientistEmail);
            } catch (\Exception $e) {
                Log::error('SWOT Analysis - Failed to create default district: ' . $e->getMessage());
                return null;
            }
        }

        return $district;
    }

    /**
     * Get the SWOT analysis for the current scientist.
     */
    public function getSwotAnalysis()
    {
        try {
            $district = $this->getScientistDistrict();

            if (!$district) {
                Log::error('SWOT Analysis Get - Failed to get or create district for scientist: ' . Auth::user()->email);
                return response()->json(['error' => 'Failed to get or create district information'], 500);
            }

            $swotAnalysis = Swot::where('scientist_id', Auth::id())
                ->where('district_id', $district->id)
                ->first();

            if (!$swotAnalysis) {
                return response()->json([
                    'message' => 'No SWOT analysis found. Please create one.',
                    'district' => [
                        'id' => $district->id,
                        'state' => $district->state,
                        'district' => $district->district,
                        'status' => $district->status
                    ]
                ]);
            }

            return response()->json([
                'swot' => $swotAnalysis,
                'district' => [
                    'id' => $district->id,
                    'state' => $district->state,
                    'district' => $district->district,
                    'status' => $district->status
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting SWOT analysis: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get SWOT analysis'], 500);
        }
    }

    /**
     * Save or update SWOT analysis.
     */
    public function saveSwotAnalysis(Request $request)
    {
        // Enhanced logging to debug the request
        Log::info('SWOT Analysis Save - Raw Request:', [
            'method' => $request->method(),
            'all' => $request->all(),
            'query' => $request->query(),
            'post' => $request->post(),
            'json' => $request->json()->all()
        ]);

        // Validate the request - use different validation for GET vs POST
        if ($request->isMethod('get')) {
            Log::info('SWOT Analysis Save - Processing GET request with query parameters');

            // For GET requests, be more lenient with validation
            $validated = $request->validate([
                'strengths' => 'required|string|max:1000',
                'weaknesses' => 'required|string|max:1000',
                'opportunities' => 'required|string|max:1000',
                'threats' => 'required|string|max:1000',
            ]);

            // Log the validated data
            Log::info('SWOT Analysis Save - Validated GET data:', $validated);
        } else {
            // For POST requests, use the standard validation
            $request->validate([
                'strengths' => 'required|string|max:1000',
                'weaknesses' => 'required|string|max:1000',
                'opportunities' => 'required|string|max:1000',
                'threats' => 'required|string|max:1000',
            ]);
        }

        try {
            // Debug logging
            Log::info('SWOT Analysis Save - User ID: ' . Auth::id());

            // Check if the table exists
            $tableExists = Schema::hasTable('swot');
            Log::info('SWOT Analysis Save - Table exists: ' . ($tableExists ? 'Yes' : 'No'));

            if (!$tableExists) {
                return response()->json(['error' => 'SWOT table does not exist'], 500);
            }

            // Get the scientist's district if district_id is not provided
            $district = null;
            if ($request->filled('district_id')) {
                $district = StateData::where('id', $request->district_id)
                    ->where('scientist', Auth::user()->email)
                    ->first();
            } else {
                $district = $this->getScientistDistrict();
            }

            if (!$district) {
                Log::error('SWOT Analysis Save - Failed to get or create district for scientist: ' . Auth::user()->email);
                return response()->json(['error' => 'Failed to get or create district information'], 500);
            }

            Log::info('SWOT Analysis Save - District found: ' . $district->id . ' - ' . $district->district);

            // Try to create a new SWOT analysis directly
            try {
                // Check if a SWOT analysis already exists for this scientist and district
                $existingSwot = Swot::where('scientist_id', Auth::id())
                    ->where('district_id', $district->id)
                    ->first();

                if ($existingSwot) {
                    // Update existing SWOT analysis
                    $existingSwot->strengths = $request->strengths;
                    $existingSwot->weaknesses = $request->weaknesses;
                    $existingSwot->opportunities = $request->opportunities;
                    $existingSwot->threats = $request->threats;
                    $existingSwot->save();

                    Log::info('SWOT Analysis Save - Updated existing record with ID: ' . $existingSwot->id);

                    // For GET requests, redirect to the SWOT page with a success message
                    if ($request->isMethod('get')) {
                        return redirect()->route('scientist.swot')
                            ->with('success', 'SWOT analysis updated successfully');
                    }

                    // For POST requests, return JSON response
                    return response()->json([
                        'message' => 'SWOT analysis updated successfully',
                        'swot' => $existingSwot
                    ]);
                } else {
                    // Create new SWOT analysis
                    $swotAnalysis = new Swot();
                    $swotAnalysis->scientist_id = Auth::id();
                    $swotAnalysis->district_id = $district->id;
                    $swotAnalysis->strengths = $request->strengths;
                    $swotAnalysis->weaknesses = $request->weaknesses;
                    $swotAnalysis->opportunities = $request->opportunities;
                    $swotAnalysis->threats = $request->threats;
                    $swotAnalysis->save();

                    Log::info('SWOT Analysis Save - Created new record with ID: ' . $swotAnalysis->id);

                    // For GET requests, redirect to the SWOT page with a success message
                    if ($request->isMethod('get')) {
                        return redirect()->route('scientist.swot')
                            ->with('success', 'SWOT analysis saved successfully');
                    }

                    // For POST requests, return JSON response
                    return response()->json([
                        'message' => 'SWOT analysis saved successfully',
                        'swot' => $swotAnalysis
                    ]);
                }
            } catch (\Exception $innerException) {
                Log::error('SWOT Analysis Save - Error saving record: ' . $innerException->getMessage());
                Log::error('SWOT Analysis Save - Stack trace: ' . $innerException->getTraceAsString());

                // For GET requests, redirect with error message
                if ($request->isMethod('get')) {
                    return redirect()->route('scientist.swot')
                        ->with('error', 'Failed to save SWOT analysis: ' . $innerException->getMessage());
                }

                // For POST requests, return JSON error response
                return response()->json([
                    'error' => 'Failed to save SWOT analysis: ' . $innerException->getMessage()
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('SWOT Analysis Save - Fatal error: ' . $e->getMessage());
            Log::error('SWOT Analysis Save - Stack trace: ' . $e->getTraceAsString());

            // For GET requests, redirect with error message
            if ($request->isMethod('get')) {
                return redirect()->route('scientist.swot')
                    ->with('error', 'Failed to save SWOT analysis: ' . $e->getMessage());
            }

            // For POST requests, return JSON error response
            return response()->json(['error' => 'Failed to save SWOT analysis: ' . $e->getMessage()], 500);
        }
    }
}
