<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class FormBuilder extends Model
{
    use HasFactory;

    protected $fillable = [
        'form_name',
        'target_user',
        'form_structure',
        'last_date',
        'last_edited_at'
    ];

    protected $casts = [
        'last_date' => 'datetime',
        'last_edited_at' => 'datetime'
    ];

    /**
     * Get the form structure attribute.
     *
     * @param  mixed  $value
     * @return array
     */
    public function getFormStructureAttribute($value)
    {
        // If the value is already an array, return it
        if (is_array($value)) {
            return $value;
        }

        // If the value is null, return an empty array
        if (is_null($value)) {
            return [];
        }

        // If the value is a string, try to decode it
        if (is_string($value)) {
            try {
                // First attempt: direct JSON decode
                $decoded = json_decode($value, true);

                if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                    return $decoded;
                }

                // Second attempt: try to clean the string by removing extra escaping
                $cleanedValue = stripslashes($value);
                $decoded = json_decode($cleanedValue, true);

                if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                    return $decoded;
                }

                // Third attempt: handle double-encoded JSON
                $doubleDecoded = json_decode(json_decode($value, true), true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($doubleDecoded)) {
                    return $doubleDecoded;
                }

                // Fourth attempt: try with regex to extract JSON array
                if (preg_match('/\[(.*)\]/', $value, $matches)) {
                    $extractedJson = '[' . $matches[1] . ']';
                    $decoded = json_decode($extractedJson, true);

                    if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                        return $decoded;
                    }
                }

                // If all attempts fail, log the error and return an empty array
                Log::error('Failed to decode form structure', [
                    'form_id' => $this->id ?? 'unknown',
                    'value' => substr($value, 0, 500), // Log first 500 chars to avoid huge logs
                    'error' => json_last_error_msg(),
                    'value_type' => gettype($value),
                    'value_length' => strlen($value)
                ]);

                return [];
            } catch (\Exception $e) {
                Log::error('Exception while decoding form structure', [
                    'form_id' => $this->id ?? 'unknown',
                    'value' => substr($value, 0, 500),
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                return [];
            }
        }

        // If we get here, the value is of an unexpected type
        Log::warning('Form structure is of unexpected type', [
            'form_id' => $this->id ?? 'unknown',
            'type' => gettype($value),
            'value' => $value
        ]);

        return [];
    }

    /**
     * Set the form structure attribute.
     *
     * @param  mixed  $value
     * @return void
     */
    public function setFormStructureAttribute($value)
    {
        // If it's already a string (JSON), store it directly
        if (is_string($value)) {
            // Validate that it's valid JSON
            json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $this->attributes['form_structure'] = $value;
            } else {
                Log::error('Invalid JSON provided for form structure', [
                    'form_id' => $this->id ?? 'unknown',
                    'value' => substr($value, 0, 500),
                    'error' => json_last_error_msg()
                ]);
                $this->attributes['form_structure'] = '[]';
            }
        }
        // If it's an array, encode it to JSON
        elseif (is_array($value)) {
            $this->attributes['form_structure'] = json_encode($value);
        }
        // If it's null, store empty array
        elseif (is_null($value)) {
            $this->attributes['form_structure'] = '[]';
        }
        // For any other type, log error and store empty array
        else {
            Log::error('Invalid type provided for form structure', [
                'form_id' => $this->id ?? 'unknown',
                'type' => gettype($value),
                'value' => $value
            ]);
            $this->attributes['form_structure'] = '[]';
        }
    }
}
