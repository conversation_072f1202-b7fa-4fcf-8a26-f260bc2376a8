@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>{{ __('Participant Reviews for: ') . $actionPlan->title }}</span>
                        <div>
                            <a href="{{ route('scientist.feedback-import.index') }}" class="btn btn-sm btn-secondary">Back to Review System</a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Action Plan Details -->
                    <div class="alert alert-info">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Action Plan:</strong> {{ $actionPlan->title }}</p>
                                <p><strong>Type:</strong> 
                                    @switch($actionPlan->type)
                                        @case('training')
                                            Training Programme
                                            @break
                                        @case('field_visit')
                                            Field Visit
                                            @break
                                        @case('demonstration')
                                            Demonstration
                                            @break
                                        @case('awareness')
                                            Awareness Program
                                            @break
                                        @default
                                            {{ $actionPlan->type }}
                                    @endswitch
                                </p>
                                <p><strong>Date:</strong> {{ $actionPlan->planned_date ? $actionPlan->planned_date->format('d M Y') : 'N/A' }}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Location:</strong> {{ $actionPlan->location ?? 'N/A' }}</p>
                                <p><strong>District:</strong> {{ $actionPlan->district->district ?? 'N/A' }}</p>
                                <p><strong>Status:</strong> 
                                    @switch($actionPlan->status)
                                        @case('planned')
                                            <span class="badge bg-info">Planned</span>
                                            @break
                                        @case('in_progress')
                                            <span class="badge bg-warning text-dark">In Progress</span>
                                            @break
                                        @case('completed')
                                            <span class="badge bg-success">Completed</span>
                                            @break
                                        @case('cancelled')
                                            <span class="badge bg-danger">Cancelled</span>
                                            @break
                                        @default
                                            <span class="badge bg-secondary">{{ $actionPlan->status }}</span>
                                    @endswitch
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Feedback Summary -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">{{ $feedback->count() }}</h5>
                                    <p class="card-text">Total Reviews</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">{{ $feedback->avg('benefit_rating') ? number_format($feedback->avg('benefit_rating'), 1) : 'N/A' }}</h5>
                                    <p class="card-text">Avg Benefit Rating</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">{{ $feedback->avg('speaker_rating') ? number_format($feedback->avg('speaker_rating'), 1) : 'N/A' }}</h5>
                                    <p class="card-text">Avg Speaker Rating</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">{{ $feedback->where('would_recommend', true)->count() }}</h5>
                                    <p class="card-text">Would Recommend</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Export Options -->
                    <div class="mb-3">
                        <a href="/scientist/feedback-import/download-template?event_id={{ $actionPlan->id }}" class="btn btn-info btn-sm">
                            <i class="fas fa-download"></i> Download Template
                        </a>
                        <button class="btn btn-success btn-sm" onclick="exportToPdf()">
                            <i class="fas fa-file-pdf"></i> Export to PDF
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="exportToExcel()">
                            <i class="fas fa-file-excel"></i> Export to Excel
                        </button>
                    </div>

                    <!-- Feedback Table -->
                    @if($feedback->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th style="color: #000 !important;">Name</th>
                                        <th style="color: #000 !important;">Phone</th>
                                        <th style="color: #000 !important;">Benefit Rating</th>
                                        <th style="color: #000 !important;">Would Recommend</th>
                                        <th style="color: #000 !important;">Most Helpful Topic</th>
                                        <th style="color: #000 !important;">Speaker Rating</th>
                                        <th style="color: #000 !important;">Submitted</th>
                                        <th style="color: #000 !important;">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($feedback as $review)
                                        <tr>
                                            <td style="color: #000 !important;">{{ $review->name }}</td>
                                            <td style="color: #000 !important;">{{ $review->phone_number }}</td>
                                            <td style="color: #000 !important;">
                                                <span class="badge bg-primary">{{ $review->benefit_rating }}/10</span>
                                            </td>
                                            <td style="color: #000 !important;">
                                                @if($review->would_recommend)
                                                    <span class="badge bg-success">Yes</span>
                                                @else
                                                    <span class="badge bg-danger">No</span>
                                                @endif
                                            </td>
                                            <td style="color: #000 !important;">{{ $review->most_helpful_topic ?? 'N/A' }}</td>
                                            <td style="color: #000 !important;">
                                                <span class="badge bg-warning text-dark">{{ $review->speaker_rating }}/5</span>
                                            </td>
                                            <td style="color: #000 !important;">{{ $review->created_at->format('d M Y H:i') }}</td>
                                            <td>
                                                <button class="btn btn-sm btn-primary" onclick="viewFeedbackDetails({{ $review->id }})">
                                                    <i class="fas fa-eye"></i> View
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-warning text-center">
                            <h5>No Reviews Yet</h5>
                            <p>No participant reviews have been submitted for this action plan yet.</p>
                            <p>You can:</p>
                            <ul class="list-unstyled">
                                <li>• Generate a QR code for participants to submit reviews</li>
                                <li>• Download the template and upload bulk reviews</li>
                                <li>• Share the feedback link with participants</li>
                            </ul>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Feedback Details Modal -->
<div class="modal fade" id="feedbackDetailsModal" tabindex="-1" aria-labelledby="feedbackDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="feedbackDetailsModalLabel">Feedback Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="feedbackDetailsBody">
                <!-- Feedback details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    const feedbackDetailsModal = new bootstrap.Modal(document.getElementById('feedbackDetailsModal'));
    const feedbackData = @json($feedback);

    function viewFeedbackDetails(feedbackId) {
        const feedback = feedbackData.find(f => f.id === feedbackId);
        
        if (!feedback) {
            alert('Feedback not found');
            return;
        }

        const html = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Participant Information</h6>
                    <p><strong>Name:</strong> ${feedback.name}</p>
                    <p><strong>Phone:</strong> ${feedback.phone_number}</p>
                    <p><strong>Submitted:</strong> ${new Date(feedback.created_at).toLocaleString()}</p>
                </div>
                <div class="col-md-6">
                    <h6>Ratings</h6>
                    <p><strong>Benefit Rating:</strong> ${feedback.benefit_rating}/10</p>
                    <p><strong>Speaker Rating:</strong> ${feedback.speaker_rating}/5</p>
                    <p><strong>Would Recommend:</strong> ${feedback.would_recommend ? 'Yes' : 'No'}</p>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-12">
                    <h6>Most Helpful Topic</h6>
                    <p>${feedback.most_helpful_topic || 'Not provided'}</p>
                </div>
            </div>
        `;

        document.getElementById('feedbackDetailsBody').innerHTML = html;
        feedbackDetailsModal.show();
    }

    function exportToPdf() {
        window.open(`/scientist/feedback-import/export-pdf/{{ $actionPlan->id }}`, '_blank');
    }

    function exportToExcel() {
        window.open(`/scientist/feedback-import/export-excel/{{ $actionPlan->id }}`, '_blank');
    }
</script>
@endsection
