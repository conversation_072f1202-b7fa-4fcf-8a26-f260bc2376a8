<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\ScientistFeedback;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class ScientistFeedbackTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a super admin user
        $this->superAdmin = User::factory()->create([
            'role' => 'super_admin',
            'email' => '<EMAIL>'
        ]);
        
        // Create a scientist user
        $this->scientist = User::factory()->create([
            'role' => 'scientist',
            'email' => '<EMAIL>'
        ]);
    }

    /** @test */
    public function super_admin_can_view_scientist_feedback_page()
    {
        $response = $this->actingAs($this->superAdmin)
            ->get(route('super-admin.scientist-feedback.index'));

        $response->assertStatus(200);
        $response->assertViewIs('super-admin.scientist-feedback.index');
    }

    /** @test */
    public function super_admin_can_create_scientist_feedback()
    {
        $feedbackData = [
            'scientist_id' => $this->scientist->id,
            'overall_rating' => 4,
            'preparedness_rating' => 4,
            'communication_rating' => 5,
            'engagement_rating' => 3,
            'adherence_rating' => 4,
            'remarks' => 'Good performance overall'
        ];

        $response = $this->actingAs($this->superAdmin)
            ->postJson(route('super-admin.scientist-feedback.save'), $feedbackData);

        $response->assertStatus(200);
        $response->assertJson(['message' => 'Feedback created successfully']);

        $this->assertDatabaseHas('scientist_feedback', [
            'scientist_id' => $this->scientist->id,
            'overall_rating' => 4,
            'preparedness_rating' => 4,
            'communication_rating' => 5,
            'engagement_rating' => 3,
            'adherence_rating' => 4,
            'created_by' => $this->superAdmin->id
        ]);
    }

    /** @test */
    public function super_admin_can_get_all_feedback()
    {
        // Create some feedback records
        ScientistFeedback::factory()->create([
            'scientist_id' => $this->scientist->id,
            'overall_rating' => 4,
            'preparedness_rating' => 4,
            'communication_rating' => 5,
            'engagement_rating' => 3,
            'adherence_rating' => 4,
            'created_by' => $this->superAdmin->id
        ]);

        $response = $this->actingAs($this->superAdmin)
            ->getJson(route('super-admin.scientist-feedback.all'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            '*' => [
                'id',
                'scientist_id',
                'overall_rating',
                'preparedness_rating',
                'communication_rating',
                'engagement_rating',
                'adherence_rating',
                'remarks',
                'created_by',
                'created_at',
                'updated_at',
                'scientist',
                'creator'
            ]
        ]);
    }

    /** @test */
    public function feedback_validation_requires_all_rating_fields()
    {
        $feedbackData = [
            'scientist_id' => $this->scientist->id,
            'overall_rating' => 4,
            // Missing required rating fields
        ];

        $response = $this->actingAs($this->superAdmin)
            ->postJson(route('super-admin.scientist-feedback.save'), $feedbackData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'preparedness_rating',
            'communication_rating',
            'engagement_rating',
            'adherence_rating'
        ]);
    }

    /** @test */
    public function feedback_validation_requires_valid_rating_range()
    {
        $feedbackData = [
            'scientist_id' => $this->scientist->id,
            'overall_rating' => 6, // Invalid - should be 1-5
            'preparedness_rating' => 0, // Invalid - should be 1-5
            'communication_rating' => 5,
            'engagement_rating' => 3,
            'adherence_rating' => 4,
        ];

        $response = $this->actingAs($this->superAdmin)
            ->postJson(route('super-admin.scientist-feedback.save'), $feedbackData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'overall_rating',
            'preparedness_rating'
        ]);
    }
}
