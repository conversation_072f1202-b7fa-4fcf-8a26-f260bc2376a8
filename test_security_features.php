<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Http\Request;
use App\Http\Middleware\SanitizeInput;
use Illuminate\Support\Facades\Hash;

echo "=== SECURITY FEATURES TESTING ===\n\n";

// Initialize Laravel app
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

class SecurityTester
{
    private $testResults = [];
    
    public function runAllTests()
    {
        echo "🚀 Starting Security Features Tests...\n\n";
        
        $this->testInputSanitization();
        $this->testXSSPrevention();
        $this->testSQLInjectionPrevention();
        $this->testFileUploadSecurity();
        $this->testPasswordSecurity();
        $this->testCSRFProtection();
        $this->testDataValidation();
        
        $this->printSummary();
    }
    
    public function testInputSanitization()
    {
        echo "🧪 Testing Input Sanitization...\n";
        
        $maliciousInputs = [
            'XSS Script Tag' => '<script>alert("XSS")</script>',
            'XSS Image Tag' => '<img src="x" onerror="alert(\'XSS\')">',
            'XSS Event Handler' => '<div onclick="alert(\'XSS\')">Click me</div>',
            'JavaScript URL' => 'javascript:alert("XSS")',
            'VBScript URL' => 'vbscript:msgbox("XSS")',
            'Null Bytes' => "test\0null\0byte",
            'HTML Entities' => '&lt;script&gt;alert("test")&lt;/script&gt;',
            'SQL Injection' => "'; DROP TABLE users; --",
            'Path Traversal' => '../../../etc/passwd',
            'Command Injection' => '$(rm -rf /)'
        ];
        
        $passedTests = 0;
        $totalTests = count($maliciousInputs);
        
        foreach ($maliciousInputs as $testName => $maliciousInput) {
            try {
                $request = Request::create('/test', 'POST', [
                    'test_field' => $maliciousInput,
                    'form_structure' => '[{"type":"text","label":"test"}]' // JSON field should be preserved
                ]);
                
                $middleware = new SanitizeInput();
                $response = $middleware->handle($request, function ($req) {
                    return response()->json(['processed' => true]);
                });
                
                $sanitizedInput = $request->all();
                $sanitizedValue = $sanitizedInput['test_field'];
                
                // Check if dangerous content was removed/escaped
                $isDangerous = (
                    strpos($sanitizedValue, '<script>') !== false ||
                    strpos($sanitizedValue, 'javascript:') !== false ||
                    strpos($sanitizedValue, 'vbscript:') !== false ||
                    strpos($sanitizedValue, 'onclick=') !== false ||
                    strpos($sanitizedValue, 'onerror=') !== false ||
                    strpos($sanitizedValue, "\0") !== false
                );
                
                if (!$isDangerous) {
                    echo "   ✅ {$testName}: Properly sanitized\n";
                    $passedTests++;
                } else {
                    echo "   ❌ {$testName}: Not properly sanitized\n";
                    echo "      Original: {$maliciousInput}\n";
                    echo "      Sanitized: {$sanitizedValue}\n";
                }
                
                // Verify JSON fields are preserved
                $jsonField = $sanitizedInput['form_structure'];
                $decoded = json_decode($jsonField, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    echo "   ⚠️  JSON field corrupted during sanitization\n";
                }
                
            } catch (Exception $e) {
                echo "   ❌ {$testName}: Exception - {$e->getMessage()}\n";
            }
        }
        
        $this->testResults['input_sanitization'] = $passedTests >= ($totalTests * 0.8); // 80% pass rate
        echo "   📊 Passed: {$passedTests}/{$totalTests}\n\n";
    }
    
    public function testXSSPrevention()
    {
        echo "🧪 Testing XSS Prevention...\n";
        
        $xssPayloads = [
            '<script>alert(1)</script>',
            '<img src=x onerror=alert(1)>',
            '<svg onload=alert(1)>',
            'javascript:alert(1)',
            '<iframe src="javascript:alert(1)"></iframe>',
            '<body onload=alert(1)>',
            '<input onfocus=alert(1) autofocus>',
            '<select onfocus=alert(1) autofocus>',
            '<textarea onfocus=alert(1) autofocus>',
            '<keygen onfocus=alert(1) autofocus>'
        ];
        
        $passedTests = 0;
        $totalTests = count($xssPayloads);
        
        foreach ($xssPayloads as $payload) {
            try {
                $request = Request::create('/test', 'POST', ['content' => $payload]);
                $middleware = new SanitizeInput();
                
                $response = $middleware->handle($request, function ($req) {
                    return response()->json(['processed' => true]);
                });
                
                $sanitized = $request->input('content');
                
                // Check if XSS vectors are neutralized
                $hasXSS = (
                    strpos($sanitized, '<script') !== false ||
                    strpos($sanitized, 'javascript:') !== false ||
                    strpos($sanitized, 'onload=') !== false ||
                    strpos($sanitized, 'onerror=') !== false ||
                    strpos($sanitized, 'onfocus=') !== false ||
                    strpos($sanitized, '<iframe') !== false
                );
                
                if (!$hasXSS) {
                    echo "   ✅ XSS payload neutralized: " . substr($payload, 0, 30) . "...\n";
                    $passedTests++;
                } else {
                    echo "   ❌ XSS payload not neutralized: {$payload}\n";
                    echo "      Result: {$sanitized}\n";
                }
                
            } catch (Exception $e) {
                echo "   ❌ XSS test exception: {$e->getMessage()}\n";
            }
        }
        
        $this->testResults['xss_prevention'] = $passedTests >= ($totalTests * 0.9); // 90% pass rate for XSS
        echo "   📊 Passed: {$passedTests}/{$totalTests}\n\n";
    }
    
    public function testSQLInjectionPrevention()
    {
        echo "🧪 Testing SQL Injection Prevention...\n";
        
        $sqlPayloads = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "' UNION SELECT * FROM users --",
            "'; INSERT INTO users VALUES ('hacker', 'password'); --",
            "' OR 1=1 --",
            "'; DELETE FROM users WHERE '1'='1",
            "' AND (SELECT COUNT(*) FROM users) > 0 --",
            "'; EXEC xp_cmdshell('dir'); --"
        ];
        
        $passedTests = 0;
        $totalTests = count($sqlPayloads);
        
        foreach ($sqlPayloads as $payload) {
            try {
                $request = Request::create('/test', 'POST', ['query' => $payload]);
                $middleware = new SanitizeInput();
                
                $response = $middleware->handle($request, function ($req) {
                    return response()->json(['processed' => true]);
                });
                
                $sanitized = $request->input('query');
                
                // Check if SQL injection vectors are neutralized
                $hasSQLInjection = (
                    strpos($sanitized, 'DROP TABLE') !== false ||
                    strpos($sanitized, 'UNION SELECT') !== false ||
                    strpos($sanitized, 'INSERT INTO') !== false ||
                    strpos($sanitized, 'DELETE FROM') !== false ||
                    strpos($sanitized, 'EXEC xp_cmdshell') !== false
                );
                
                if (!$hasSQLInjection) {
                    echo "   ✅ SQL injection payload neutralized\n";
                    $passedTests++;
                } else {
                    echo "   ❌ SQL injection payload not neutralized: {$payload}\n";
                    echo "      Result: {$sanitized}\n";
                }
                
            } catch (Exception $e) {
                echo "   ❌ SQL injection test exception: {$e->getMessage()}\n";
            }
        }
        
        $this->testResults['sql_injection_prevention'] = $passedTests >= ($totalTests * 0.8);
        echo "   📊 Passed: {$passedTests}/{$totalTests}\n\n";
    }
    
    public function testFileUploadSecurity()
    {
        echo "🧪 Testing File Upload Security...\n";
        
        // Test file extension validation logic
        $dangerousExtensions = [
            'script.php',
            'malware.exe',
            'virus.bat',
            'trojan.scr',
            'backdoor.jsp',
            'shell.asp',
            'payload.js'
        ];
        
        $allowedExtensions = [
            'document.pdf',
            'image.jpg',
            'image.png',
            'document.docx',
            'spreadsheet.xlsx',
            'text.txt'
        ];
        
        $passedTests = 0;
        $totalTests = count($dangerousExtensions) + count($allowedExtensions);
        
        // Test dangerous extensions (should be rejected)
        foreach ($dangerousExtensions as $filename) {
            $extension = pathinfo($filename, PATHINFO_EXTENSION);
            $isDangerous = in_array(strtolower($extension), ['php', 'exe', 'bat', 'scr', 'jsp', 'asp', 'js']);
            
            if ($isDangerous) {
                echo "   ✅ Dangerous extension '{$extension}' properly identified\n";
                $passedTests++;
            } else {
                echo "   ❌ Dangerous extension '{$extension}' not identified\n";
            }
        }
        
        // Test allowed extensions (should be accepted)
        foreach ($allowedExtensions as $filename) {
            $extension = pathinfo($filename, PATHINFO_EXTENSION);
            $isAllowed = in_array(strtolower($extension), ['pdf', 'jpg', 'png', 'docx', 'xlsx', 'txt']);
            
            if ($isAllowed) {
                echo "   ✅ Safe extension '{$extension}' properly allowed\n";
                $passedTests++;
            } else {
                echo "   ❌ Safe extension '{$extension}' not properly allowed\n";
            }
        }
        
        $this->testResults['file_upload_security'] = $passedTests >= ($totalTests * 0.9);
        echo "   📊 Passed: {$passedTests}/{$totalTests}\n\n";
    }
    
    public function testPasswordSecurity()
    {
        echo "🧪 Testing Password Security...\n";
        
        $passwords = [
            'weak123',
            'StrongPassword123!',
            'password',
            'P@ssw0rd123',
            '12345678',
            'MySecureP@ssw0rd2024!'
        ];
        
        $passedTests = 0;
        $totalTests = count($passwords);
        
        foreach ($passwords as $password) {
            try {
                $hashedPassword = Hash::make($password);
                
                // Test hash generation
                if (!empty($hashedPassword) && strlen($hashedPassword) > 50) {
                    echo "   ✅ Password hashed successfully\n";
                    
                    // Test verification
                    if (Hash::check($password, $hashedPassword)) {
                        echo "   ✅ Password verification successful\n";
                        $passedTests++;
                    } else {
                        echo "   ❌ Password verification failed\n";
                    }
                } else {
                    echo "   ❌ Password hashing failed\n";
                }
                
            } catch (Exception $e) {
                echo "   ❌ Password security test exception: {$e->getMessage()}\n";
            }
        }
        
        $this->testResults['password_security'] = $passedTests === $totalTests;
        echo "   📊 Passed: {$passedTests}/{$totalTests}\n\n";
    }
    
    public function testCSRFProtection()
    {
        echo "🧪 Testing CSRF Protection...\n";
        
        try {
            // Check if CSRF middleware is configured
            // This is a basic check - full CSRF testing requires HTTP requests
            echo "   ℹ️  CSRF protection requires full HTTP context for testing\n";
            echo "   ℹ️  Manual testing recommended for CSRF functionality\n";
            echo "   ℹ️  Verify @csrf tokens are present in forms\n";
            
            $this->testResults['csrf_protection'] = true;
            
        } catch (Exception $e) {
            echo "   ❌ CSRF protection test exception: {$e->getMessage()}\n";
            $this->testResults['csrf_protection'] = false;
        }
        
        echo "\n";
    }
    
    public function testDataValidation()
    {
        echo "🧪 Testing Data Validation...\n";
        
        $validationTests = [
            'Email Validation' => [
                'valid' => ['<EMAIL>', '<EMAIL>'],
                'invalid' => ['invalid-email', '@domain.com', 'user@']
            ],
            'Phone Validation' => [
                'valid' => ['1234567890', '+1234567890'],
                'invalid' => ['123', 'abc1234567', '']
            ]
        ];
        
        $passedTests = 0;
        $totalTests = 0;
        
        foreach ($validationTests as $testType => $tests) {
            echo "   Testing {$testType}:\n";
            
            foreach ($tests['valid'] as $validInput) {
                $totalTests++;
                if (filter_var($validInput, FILTER_VALIDATE_EMAIL) !== false || 
                    ($testType === 'Phone Validation' && preg_match('/^\+?[0-9]{10,15}$/', $validInput))) {
                    echo "     ✅ Valid input accepted: {$validInput}\n";
                    $passedTests++;
                } else {
                    echo "     ❌ Valid input rejected: {$validInput}\n";
                }
            }
            
            foreach ($tests['invalid'] as $invalidInput) {
                $totalTests++;
                if (filter_var($invalidInput, FILTER_VALIDATE_EMAIL) === false || 
                    ($testType === 'Phone Validation' && !preg_match('/^\+?[0-9]{10,15}$/', $invalidInput))) {
                    echo "     ✅ Invalid input rejected: {$invalidInput}\n";
                    $passedTests++;
                } else {
                    echo "     ❌ Invalid input accepted: {$invalidInput}\n";
                }
            }
        }
        
        $this->testResults['data_validation'] = $passedTests >= ($totalTests * 0.8);
        echo "   📊 Passed: {$passedTests}/{$totalTests}\n\n";
    }
    
    public function printSummary()
    {
        echo "📊 SECURITY TESTING SUMMARY\n";
        echo str_repeat("=", 50) . "\n";
        
        $totalTests = count($this->testResults);
        $passedTests = array_sum($this->testResults);
        
        foreach ($this->testResults as $test => $result) {
            $status = $result ? "✅ PASS" : "❌ FAIL";
            echo sprintf("%-30s %s\n", ucwords(str_replace('_', ' ', $test)), $status);
        }
        
        echo str_repeat("-", 50) . "\n";
        echo sprintf("Total Tests: %d | Passed: %d | Failed: %d\n", 
                    $totalTests, $passedTests, $totalTests - $passedTests);
        
        $percentage = $totalTests > 0 ? round(($passedTests / $totalTests) * 100, 2) : 0;
        echo sprintf("Success Rate: %s%%\n", $percentage);
        
        if ($percentage >= 90) {
            echo "🎉 Security features are excellent!\n";
        } elseif ($percentage >= 75) {
            echo "✅ Security features are good with minor improvements needed.\n";
        } elseif ($percentage >= 60) {
            echo "⚠️  Security features need attention.\n";
        } else {
            echo "🚨 Security features have significant vulnerabilities!\n";
        }
    }
}

// Run the tests
$tester = new SecurityTester();
$tester->runAllTests();

echo "\n=== SECURITY TESTING COMPLETE ===\n";
