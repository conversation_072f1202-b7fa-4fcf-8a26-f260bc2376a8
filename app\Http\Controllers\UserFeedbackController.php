<?php

namespace App\Http\Controllers;

use App\Models\ActionPlan;
use App\Models\UserFeedback;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\UserFeedbackExport;
use App\Imports\UserFeedbackImport;

class UserFeedbackController extends Controller
{
    public function showFeedbackForm($actionPlanId)
    {
        $actionPlan = ActionPlan::findOrFail($actionPlanId);
        return view('feedback.form', compact('actionPlan'));
    }

    public function submitFeedback(Request $request)
    {
        $request->validate([
            'action_plan_id' => 'required|exists:action_plans,id',
            'name' => 'required|string|max:255',
            'contact' => 'required|string|max:15',
            'benefit_rating' => 'required|integer|min:1|max:10',
            'would_recommend' => 'required|boolean',
            'most_helpful_topic' => 'nullable|string|max:255',
            'speaker_rating' => 'required|integer|min:1|max:5',
            'improvement_suggestions' => 'nullable|string'
        ]);

        try {
            // Check for existing feedback
            $existingFeedback = UserFeedback::where('contact', $request->contact)
                ->where('action_plan_id', $request->action_plan_id)
                ->first();

            if ($existingFeedback) {
                return response()->json([
                    'error' => 'Feedback already submitted for this action plan'
                ], 400);
            }

            UserFeedback::create($request->all());

            return response()->json([
                'message' => 'Feedback submitted successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error submitting feedback: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to submit feedback'
            ], 500);
        }
    }

    public function downloadTemplate()
    {
        return Excel::download(new UserFeedbackExport, 'feedback_template.xlsx');
    }

    public function importFeedback(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls'
        ]);

        try {
            Excel::import(new UserFeedbackImport, $request->file('file'));

            return response()->json([
                'message' => 'Feedback imported successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error importing feedback: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to import feedback'
            ], 500);
        }
    }

    public function getFeedbackList($actionPlanId)
    {
        $feedback = UserFeedback::where('action_plan_id', $actionPlanId)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json($feedback);
    }
}
