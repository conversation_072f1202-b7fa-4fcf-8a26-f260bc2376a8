<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Feedback - {{ $event->title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 20px;
            padding-bottom: 20px;
        }
        .feedback-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header img {
            max-width: 200px;
            margin-bottom: 20px;
        }
        .rating-container {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            margin: 20px 0;
        }
        .rating-btn {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            border: 2px solid #dee2e6;
            background-color: #fff;
            font-weight: bold;
            transition: all 0.2s;
        }
        .rating-btn:hover {
            transform: scale(1.1);
            border-color: #6c757d;
        }
        .rating-btn.selected {
            background-color: #0d6efd;
            color: white;
            border-color: #0d6efd;
        }
        .speaker-rating {
            display: flex;
            flex-direction: row;
            font-size: 30px;
            color: #dee2e6;
        }
        .speaker-rating i {
            margin-right: 10px;
            cursor: pointer;
        }
        .speaker-rating i.selected {
            color: #ffc107;
        }
        .form-footer {
            margin-top: 30px;
            text-align: center;
        }
        .event-details {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="feedback-container">
            <div class="header">
                <img src="{{ asset('images/logo.png') }}" alt="Mera Resham Mera Abhimaan" onerror="this.src='https://via.placeholder.com/200x80?text=Mera+Resham+Mera+Abhimaan'">
                <h2>Event Feedback</h2>
                <p class="text-muted">Your feedback helps us improve our programs</p>
            </div>
            
            <div class="event-details">
                <h4>{{ $event->title }}</h4>
                <p><strong>Date:</strong> {{ \Carbon\Carbon::parse($event->start_date)->format('d M Y') }}</p>
                <p><strong>Location:</strong> {{ $event->location }}</p>
                <p><strong>Topic:</strong> {{ $event->topic }}</p>
            </div>
            
            <form id="feedbackForm" action="{{ route('public.feedback.submit', ['eventId' => $event->id]) }}" method="POST">
                @csrf
                
                <div class="mb-3">
                    <label for="participant_name" class="form-label">Your Name (Optional)</label>
                    <input type="text" class="form-control" id="participant_name" name="participant_name">
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="participant_email" class="form-label">Email (Optional)</label>
                        <input type="email" class="form-control" id="participant_email" name="participant_email">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="participant_phone" class="form-label">Phone Number (Optional)</label>
                        <input type="text" class="form-control" id="participant_phone" name="participant_phone">
                    </div>
                </div>
                
                <div class="mb-4">
                    <label class="form-label">How beneficial was this event for you? (1-10)</label>
                    <input type="hidden" id="benefit_rating" name="benefit_rating" required>
                    <div class="rating-container">
                        @for ($i = 1; $i <= 10; $i++)
                            <button type="button" class="rating-btn" data-value="{{ $i }}" onclick="selectRating(this, 'benefit_rating')">{{ $i }}</button>
                        @endfor
                    </div>
                </div>
                
                <div class="mb-4">
                    <label class="form-label">Would you recommend this event to your peers?</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="would_recommend" id="recommend_yes" value="1" checked>
                        <label class="form-check-label" for="recommend_yes">
                            Yes
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="would_recommend" id="recommend_no" value="0">
                        <label class="form-check-label" for="recommend_no">
                            No
                        </label>
                    </div>
                </div>
                
                <div class="mb-4">
                    <label for="most_helpful_topic" class="form-label">Which topic did you find most helpful?</label>
                    <input type="text" class="form-control" id="most_helpful_topic" name="most_helpful_topic">
                </div>
                
                <div class="mb-4">
                    <label class="form-label">Was the speaker/SME helpful and clear in explanation? (1-5)</label>
                    <input type="hidden" id="speaker_rating" name="speaker_rating" required>
                    <div class="speaker-rating">
                        <i class="far fa-star" data-value="1" onclick="selectStar(this)"></i>
                        <i class="far fa-star" data-value="2" onclick="selectStar(this)"></i>
                        <i class="far fa-star" data-value="3" onclick="selectStar(this)"></i>
                        <i class="far fa-star" data-value="4" onclick="selectStar(this)"></i>
                        <i class="far fa-star" data-value="5" onclick="selectStar(this)"></i>
                    </div>
                </div>
                
                <div class="mb-4">
                    <label for="improvement_suggestions" class="form-label">Any suggestions for improvement?</label>
                    <textarea class="form-control" id="improvement_suggestions" name="improvement_suggestions" rows="3"></textarea>
                </div>
                
                <div class="form-footer">
                    <button type="submit" class="btn btn-primary btn-lg">Submit Feedback</button>
                </div>
            </form>
            
            <div class="mt-4 text-center">
                <p class="text-muted">Thank you for participating in our event!</p>
            </div>
        </div>
    </div>
    
    <script>
        function selectRating(button, inputId) {
            // Remove selected class from all buttons
            const buttons = document.querySelectorAll('.rating-btn');
            buttons.forEach(btn => btn.classList.remove('selected'));
            
            // Add selected class to clicked button
            button.classList.add('selected');
            
            // Update hidden input value
            document.getElementById(inputId).value = button.getAttribute('data-value');
        }
        
        function selectStar(star) {
            const value = parseInt(star.getAttribute('data-value'));
            const stars = document.querySelectorAll('.speaker-rating i');
            
            // Update hidden input value
            document.getElementById('speaker_rating').value = value;
            
            // Update star appearance
            stars.forEach((s, index) => {
                if (index < value) {
                    s.classList.remove('far');
                    s.classList.add('fas');
                    s.classList.add('selected');
                } else {
                    s.classList.remove('fas');
                    s.classList.add('far');
                    s.classList.remove('selected');
                }
            });
        }
        
        document.getElementById('feedbackForm').addEventListener('submit', function(e) {
            const benefitRating = document.getElementById('benefit_rating').value;
            const speakerRating = document.getElementById('speaker_rating').value;
            
            if (!benefitRating || !speakerRating) {
                e.preventDefault();
                alert('Please provide ratings for all required fields.');
            }
        });
    </script>
</body>
</html>
