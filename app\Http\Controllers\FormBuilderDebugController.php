<?php

namespace App\Http\Controllers;

use App\Models\FormBuilder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class FormBuilderDebugController extends Controller
{
    /**
     * Debug form structure issues
     */
    public function debugForm($formId)
    {
        try {
            $form = FormBuilder::findOrFail($formId);
            
            $debug = [
                'form_id' => $form->id,
                'form_name' => $form->form_name,
                'raw_form_structure' => $form->getAttributes()['form_structure'] ?? null,
                'parsed_form_structure' => $form->form_structure,
                'structure_type' => gettype($form->getAttributes()['form_structure'] ?? null),
                'parsed_type' => gettype($form->form_structure),
                'is_valid_json' => false,
                'json_error' => null,
                'field_count' => 0,
                'fields_valid' => false
            ];
            
            // Test JSON validity
            if (is_string($form->getAttributes()['form_structure'])) {
                $decoded = json_decode($form->getAttributes()['form_structure'], true);
                $debug['is_valid_json'] = json_last_error() === JSON_ERROR_NONE;
                $debug['json_error'] = json_last_error_msg();
            }
            
            // Test parsed structure
            if (is_array($form->form_structure)) {
                $debug['field_count'] = count($form->form_structure);
                $debug['fields_valid'] = $this->validateFields($form->form_structure);
            }
            
            return response()->json($debug, 200, [], JSON_PRETTY_PRINT);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }
    
    /**
     * Test form creation with sample data
     */
    public function testFormCreation()
    {
        try {
            $sampleFormStructure = [
                [
                    'type' => 'text',
                    'label' => 'Test Field 1',
                    'required' => true,
                    'placeholder' => 'Enter test value'
                ],
                [
                    'type' => 'email',
                    'label' => 'Test Email',
                    'required' => true,
                    'placeholder' => 'Enter email'
                ],
                [
                    'type' => 'select',
                    'label' => 'Test Select',
                    'required' => false,
                    'options' => ['Option 1', 'Option 2', 'Option 3']
                ]
            ];
            
            $form = FormBuilder::create([
                'form_name' => 'Debug Test Form - ' . now()->format('Y-m-d H:i:s'),
                'target_user' => 'all_scientists',
                'form_structure' => $sampleFormStructure,
                'last_date' => null
            ]);
            
            return response()->json([
                'message' => 'Test form created successfully',
                'form_id' => $form->id,
                'debug_url' => route('debug.form', $form->id)
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }
    
    /**
     * List all forms with their structure status
     */
    public function listFormsStatus()
    {
        try {
            $forms = FormBuilder::all();
            $results = [];
            
            foreach ($forms as $form) {
                $status = [
                    'id' => $form->id,
                    'name' => $form->form_name,
                    'target_user' => $form->target_user,
                    'raw_structure_type' => gettype($form->getAttributes()['form_structure'] ?? null),
                    'parsed_structure_type' => gettype($form->form_structure),
                    'is_array' => is_array($form->form_structure),
                    'field_count' => is_array($form->form_structure) ? count($form->form_structure) : 0,
                    'has_valid_fields' => false
                ];
                
                if (is_array($form->form_structure)) {
                    $status['has_valid_fields'] = $this->validateFields($form->form_structure);
                }
                
                $results[] = $status;
            }
            
            return response()->json($results, 200, [], JSON_PRETTY_PRINT);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }
    
    /**
     * Validate form fields structure
     */
    private function validateFields($fields)
    {
        if (!is_array($fields) || empty($fields)) {
            return false;
        }
        
        foreach ($fields as $field) {
            if (!is_array($field) || !isset($field['type']) || !isset($field['label'])) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Fix corrupted form structures
     */
    public function fixCorruptedForms()
    {
        try {
            $forms = FormBuilder::all();
            $fixed = 0;
            $errors = [];
            
            foreach ($forms as $form) {
                try {
                    $structure = $form->form_structure;
                    
                    // If structure is empty or invalid, skip
                    if (!is_array($structure) || empty($structure)) {
                        $errors[] = "Form {$form->id} ({$form->form_name}) has invalid structure";
                        continue;
                    }
                    
                    // Validate and fix fields
                    $validFields = [];
                    foreach ($structure as $field) {
                        if (is_array($field) && isset($field['type']) && isset($field['label'])) {
                            $validFields[] = $field;
                        }
                    }
                    
                    if (count($validFields) !== count($structure)) {
                        // Update the form with valid fields only
                        $form->form_structure = $validFields;
                        $form->save();
                        $fixed++;
                    }
                    
                } catch (\Exception $e) {
                    $errors[] = "Error fixing form {$form->id}: " . $e->getMessage();
                }
            }
            
            return response()->json([
                'message' => "Fixed {$fixed} forms",
                'errors' => $errors
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }
}
