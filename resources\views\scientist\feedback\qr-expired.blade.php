@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>{{ __('QR Code Expired') }}</span>
                        <div>
                            <a href="{{ route('scientist.feedback-import.index') }}" class="btn btn-sm btn-secondary">Back to Review System</a>
                        </div>
                    </div>
                </div>

                <div class="card-body text-center">
                    <!-- Action Plan Details -->
                    <div class="alert alert-info">
                        <h5>{{ $actionPlan->title }}</h5>
                        <p><strong>Date:</strong> {{ $actionPlan->planned_date ? $actionPlan->planned_date->format('d M Y') : 'N/A' }}</p>
                        <p><strong>Location:</strong> {{ $actionPlan->location ?? 'N/A' }}</p>
                    </div>

                    <!-- Expired Message -->
                    <div class="alert alert-danger">
                        <i class="fas fa-clock fa-3x mb-3"></i>
                        <h4>QR Code Generation Expired</h4>
                        <p>The time limit for generating QR codes for public feedback has expired.</p>
                        <p><strong>Time Limit:</strong> 2 days from event date</p>
                        <p><strong>Days Since Event:</strong> {{ $daysDifference }} days</p>
                    </div>

                    <!-- Alternative Options -->
                    <div class="alert alert-info">
                        <h5><i class="fas fa-lightbulb"></i> Alternative Options</h5>
                        <p>You can still collect feedback using these methods:</p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-upload text-primary"></i> <strong>Bulk Upload:</strong> Download the template and upload feedback data manually</li>
                            <li><i class="fas fa-file-excel text-success"></i> <strong>Excel Import:</strong> Collect feedback offline and import via Excel file</li>
                            <li><i class="fas fa-phone text-info"></i> <strong>Phone/Email:</strong> Contact participants directly for feedback</li>
                        </ul>
                    </div>

                    <!-- Actions -->
                    <div class="mt-4">
                        <a href="/scientist/feedback-import/download-template?event_id={{ $actionPlan->id }}" class="btn btn-primary">
                            <i class="fas fa-download"></i> Download Template
                        </a>
                        <a href="{{ route('scientist.feedback-import.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Review System
                        </a>
                    </div>

                    <!-- Help Text -->
                    <div class="mt-4">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i> 
                            The 2-day limit ensures timely feedback collection while the event is still fresh in participants' minds.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
