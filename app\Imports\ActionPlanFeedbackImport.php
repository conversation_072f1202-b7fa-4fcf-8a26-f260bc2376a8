<?php

namespace App\Imports;

use App\Models\ActionPlanFeedback;
use Maatwe<PERSON>ite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class ActionPlanFeedbackImport implements ToModel, WithHeadingRow, WithValidation, WithBatchInserts, WithChunkReading
{
    use Importable;

    protected $actionPlanId;

    public function __construct($actionPlanId)
    {
        $this->actionPlanId = $actionPlanId;
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        return new ActionPlanFeedback([
            'action_plan_id' => $this->actionPlanId,
            'event_success_rating' => $row['event_success_rating'],
            'challenges_faced' => $row['challenges_faced'] ?? null,
            'suggestions_for_improvement' => $row['suggestions_for_improvement'] ?? null,
            'objectives_met' => $this->parseArray($row['objectives_met'] ?? null),
            'self_assessed_learning_outcome' => $row['self_assessed_learning_outcome'] ?? null,
            'male_participants' => $row['male_participants'] ?? 0,
            'female_participants' => $row['female_participants'] ?? 0,
            'transgender_participants' => $row['transgender_participants'] ?? 0,
            'st_participants' => $row['st_participants'] ?? 0,
            'sc_participants' => $row['sc_participants'] ?? 0,
            'general_participants' => $row['general_participants'] ?? 0,
            'obc_participants' => $row['obc_participants'] ?? 0,
            'gis_location' => $row['gis_location'] ?? null,
        ]);
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'event_success_rating' => 'required|integer|min:1|max:10',
            'male_participants' => 'nullable|integer|min:0',
            'female_participants' => 'nullable|integer|min:0',
            'transgender_participants' => 'nullable|integer|min:0',
            'st_participants' => 'nullable|integer|min:0',
            'sc_participants' => 'nullable|integer|min:0',
            'general_participants' => 'nullable|integer|min:0',
            'obc_participants' => 'nullable|integer|min:0',
        ];
    }

    /**
     * @return array
     */
    public function customValidationMessages()
    {
        return [
            'event_success_rating.required' => 'The event success rating field is required.',
            'event_success_rating.integer' => 'The event success rating must be an integer.',
            'event_success_rating.min' => 'The event success rating must be at least 1.',
            'event_success_rating.max' => 'The event success rating may not be greater than 10.',
            '*.participants.integer' => 'The number of participants must be an integer.',
            '*.participants.min' => 'The number of participants cannot be negative.',
        ];
    }

    /**
     * @return int
     */
    public function batchSize(): int
    {
        return 100;
    }

    /**
     * @return int
     */
    public function chunkSize(): int
    {
        return 100;
    }

    /**
     * Parse comma-separated values into an array
     */
    private function parseArray($value)
    {
        if (empty($value)) {
            return [];
        }

        if (is_array($value)) {
            return $value;
        }

        return array_map('trim', explode(',', $value));
    }
}
