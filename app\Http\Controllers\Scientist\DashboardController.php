<?php

namespace App\Http\Controllers\Scientist;

use App\Http\Controllers\Controller;
use App\Models\ActionPlan;
use App\Models\FormBuilder;
use App\Models\StateData;
use App\Models\Swot;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

class DashboardController extends Controller
{
    /**
     * Show the scientist dashboard.
     */
    public function index()
    {
        // Get the scientist's district
        $scientistEmail = Auth::user()->email;
        $district = StateData::where('scientist', $scientistEmail)->first();

        // Get dashboard stats
        $stats = [
            'district' => $district ? $district->district : 'Not Assigned',
            'district_status' => $district ? $district->status : 'N/A',
            'swot_completed' => false,
            'action_plans_count' => 0,
            'completed_action_plans' => 0,
            'pending_feedback' => 0,
            'form_submissions' => 0
        ];

        // Check if SWOT analysis is completed
        if ($district) {
            $swotAnalysis = Swot::where('scientist_id', Auth::id())
                ->where('district_id', $district->id)
                ->first();

            $stats['swot_completed'] = $swotAnalysis ? true : false;
        }

        // Get action plan stats
        $actionPlans = ActionPlan::where('scientist_id', Auth::id())->get();
        $stats['action_plans_count'] = $actionPlans->count();
        $stats['completed_action_plans'] = $actionPlans->where('status', 'completed')->count();
        $stats['planned_action_plans'] = $actionPlans->where('status', 'planned')->count();
        $stats['cancelled_action_plans'] = $actionPlans->where('status', 'cancelled')->count();

        // Get pending feedback count
        $completedPlansIds = $actionPlans->where('status', 'completed')->pluck('id')->toArray();
        $feedbackSubmittedIds = \App\Models\ScientistEventFeedback::whereIn('action_plan_id', $completedPlansIds)
            ->pluck('action_plan_id')
            ->toArray();
        $stats['pending_feedback'] = count(array_diff($completedPlansIds, $feedbackSubmittedIds));

        // Get form submissions count from form-specific tables
        $stats['form_submissions'] = $this->countUserFormSubmissions(Auth::id());

        return view('scientist.dashboard', compact('stats'));
    }

    /**
     * Get dashboard statistics.
     */
    public function getStats()
    {
        // Get the scientist's district
        $scientistEmail = Auth::user()->email;
        $district = StateData::where('scientist', $scientistEmail)->first();

        // Get dashboard stats
        $stats = [
            'district' => $district ? $district->district : 'Not Assigned',
            'district_status' => $district ? $district->status : 'N/A',
            'swot_completed' => false,
            'action_plans_count' => 0,
            'completed_action_plans' => 0,
            'pending_feedback' => 0,
            'form_submissions' => 0
        ];

        // Check if SWOT analysis is completed
        if ($district) {
            $swotAnalysis = Swot::where('scientist_id', Auth::id())
                ->where('district_id', $district->id)
                ->first();

            $stats['swot_completed'] = $swotAnalysis ? true : false;
        }

        // Get action plan stats
        $actionPlans = ActionPlan::where('scientist_id', Auth::id())->get();
        $stats['action_plans_count'] = $actionPlans->count();
        $stats['completed_action_plans'] = $actionPlans->where('status', 'completed')->count();
        $stats['planned_action_plans'] = $actionPlans->where('status', 'planned')->count();
        $stats['cancelled_action_plans'] = $actionPlans->where('status', 'cancelled')->count();

        // Get pending feedback count
        $completedPlansIds = $actionPlans->where('status', 'completed')->pluck('id')->toArray();
        $feedbackSubmittedIds = \App\Models\ScientistEventFeedback::whereIn('action_plan_id', $completedPlansIds)
            ->pluck('action_plan_id')
            ->toArray();
        $stats['pending_feedback'] = count(array_diff($completedPlansIds, $feedbackSubmittedIds));

        // Get form submissions count from form-specific tables
        $stats['form_submissions'] = $this->countUserFormSubmissions(Auth::id());

        return response()->json($stats);
    }

    /**
     * Count form submissions for a user across all form-specific tables.
     *
     * @param int $userId
     * @return int
     */
    private function countUserFormSubmissions($userId)
    {
        $totalCount = 0;

        // Get all forms
        $forms = FormBuilder::all();

        foreach ($forms as $form) {
            $tableName = 'form_' . Str::slug($form->form_name, '_');

            // Check if the table exists
            if (Schema::hasTable($tableName)) {
                // Count submissions for this user in this table
                $count = DB::table($tableName)
                    ->where('user_id', $userId)
                    ->count();

                $totalCount += $count;
            }
        }

        return $totalCount;
    }
}
