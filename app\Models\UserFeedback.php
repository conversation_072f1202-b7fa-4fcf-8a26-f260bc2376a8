<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserFeedback extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_id', // This stores action_plan_id (keeping column name as event_id as per your table structure)
        'name',
        'phone_number',
        'benefit_rating',
        'would_recommend',
        'most_helpful_topic',
        'speaker_rating'
    ];

    protected $casts = [
        'benefit_rating' => 'integer',
        'would_recommend' => 'boolean',
        'speaker_rating' => 'integer'
    ];

    public function actionPlan()
    {
        return $this->belongsTo(ActionPlan::class, 'event_id');
    }
}
