<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\FormBuilder;
use App\Models\FormSubmission;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class FormBuilderController extends Controller
{
    public function index()
    {
        $forms = FormBuilder::all();
        return view('formBuilder.index', compact('forms'));
    }

    public function create(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'form_name' => 'required|string|max:255',
            'form_structure' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Handle form_structure - it might be a string (JSON) or already an array
        if (is_string($request->form_structure)) {
            $formStructureJson = $request->form_structure;
        } else {
            // form_structure is already an array (from JSON request)
            $formStructureJson = json_encode($request->form_structure);
        }

        $form = FormBuilder::create([
            'form_name' => $request->form_name,
            'form_structure' => $formStructureJson,
        ]);

        return response()->json(['message' => 'Form created successfully', 'form' => $form]);
    }

    public function editData($id)
    {
        $form = FormBuilder::findOrFail($id);
        return response()->json($form);
    }

    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'form_name' => 'required|string|max:255',
            'form_structure' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Handle form_structure - it might be a string (JSON) or already an array
        if (is_string($request->form_structure)) {
            $formStructureJson = $request->form_structure;
        } else {
            // form_structure is already an array (from JSON request)
            $formStructureJson = json_encode($request->form_structure);
        }

        $form = FormBuilder::findOrFail($id);
        $form->update([
            'form_name' => $request->form_name,
            'form_structure' => $formStructureJson,
        ]);

        return response()->json(['message' => 'Form updated successfully', 'form' => $form]);
    }

    public function destroy($id)
    {
        $form = FormBuilder::findOrFail($id);
        $form->delete();
        return response()->json(['message' => 'Form deleted successfully']);
    }

    public function read()
    {
        $forms = FormBuilder::all();
        return response()->json($forms);
    }

    /**
     * Handle form submission and create dynamic table.
     */
    public function submitForm(Request $request)
    {
        try {
            Log::info('Form submission received', ['request_type' => $request->method(), 'has_files' => $request->hasFile('*')]);

            // Get the form ID
            $formId = $request->input('form_id');

            // If form_id is not in the input, check if it's in the FormData
            if (!$formId && $request->has('form_id')) {
                $formId = $request->form_id;
            }

            if (!$formId) {
                return response()->json(['error' => 'Form ID is required'], 422);
            }

            // Get the form details
            $form = FormBuilder::findOrFail($formId);
            $formName = $form->form_name;

            // Ensure form structure is properly parsed as an array
            $formStructure = $form->form_structure;

            // If it's already an array (from the accessor), use it directly
            if (is_array($formStructure)) {
                Log::info('Form structure is already an array from accessor', [
                    'field_count' => count($formStructure)
                ]);
            }
            // If form_structure is a string, try to decode it
            elseif (is_string($formStructure)) {
                Log::info('Form structure is a string, attempting to decode', ['structure' => substr($formStructure, 0, 200)]);

                // First attempt: direct JSON decode
                $formStructure = json_decode($formStructure, true);

                // If decoding fails, try to decode after removing escaped quotes
                if (json_last_error() !== JSON_ERROR_NONE) {
                    Log::warning('JSON decode failed, trying to clean the string', ['error' => json_last_error_msg()]);
                    $cleanedStructure = stripslashes($formStructure);
                    $formStructure = json_decode($cleanedStructure, true);

                    // If that still fails, try one more approach with different cleaning
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        Log::warning('Second JSON decode failed, trying another approach', ['error' => json_last_error_msg()]);
                        // Remove any potential double encoding
                        $cleanedStructure = preg_replace('/\\\\"/', '"', $formStructure);
                        $cleanedStructure = preg_replace('/\\\\\\\\"/', '"', $cleanedStructure);
                        $formStructure = json_decode($cleanedStructure, true);

                        if (json_last_error() !== JSON_ERROR_NONE) {
                            Log::error('Failed to decode form structure JSON', [
                                'error' => json_last_error_msg(),
                                'raw_structure' => $formStructure
                            ]);
                            throw new \Exception('Invalid form structure format');
                        }
                    }
                }
            }

            // Final check to ensure we have a valid array
            if (!is_array($formStructure)) {
                Log::error('Form structure is not an array after processing', [
                    'type' => gettype($formStructure),
                    'value' => $formStructure,
                    'form_id' => $formId
                ]);

                // Try one last approach - if it's still a string, try to extract array from the string
                if (is_string($formStructure) && preg_match('/\[(.*)\]/', $formStructure, $matches)) {
                    $extractedJson = '[' . $matches[1] . ']';
                    $formStructure = json_decode($extractedJson, true);

                    if (json_last_error() !== JSON_ERROR_NONE || !is_array($formStructure)) {
                        throw new \Exception('Form structure must be an array - all parsing attempts failed');
                    }

                    Log::info('Successfully extracted array from string using regex', [
                        'extracted_count' => count($formStructure)
                    ]);
                } else {
                    throw new \Exception('Form structure must be an array');
                }
            }

            Log::info('Processed form structure', [
                'count' => count($formStructure),
                'form_id' => $formId,
                'first_field' => isset($formStructure[0]) ? json_encode($formStructure[0]) : 'none'
            ]);

            // Create a table name from the form name (sanitized)
            $tableName = 'form_' . Str::slug($formName, '_');

            // Process form data based on request type
            $formData = [];
            $fileData = [];

            // Check if this is a multipart form with files
            $hasFiles = false;
            foreach ($request->all() as $key => $value) {
                if ($request->hasFile($key)) {
                    $hasFiles = true;
                    break;
                }
            }

            if ($hasFiles) {
                Log::info('Processing multipart form with files');

                // Process all form fields
                foreach ($request->all() as $key => $value) {
                    // Skip the form_id and _token fields
                    if ($key !== 'form_id' && $key !== '_token') {
                        // Only include non-empty values
                        if ($value !== null && $value !== '') {
                            $formData[$key] = $value;
                        }
                    }
                }

                // Process file uploads
                foreach ($formStructure as $field) {
                    if (isset($field['label']) && $field['type'] === 'file') {
                        $fieldName = $field['label'];

                        if ($request->hasFile($fieldName)) {
                            try {
                                $file = $request->file($fieldName);
                                $fileName = time() . '_' . $file->getClientOriginalName();
                                $filePath = $file->storeAs('form_uploads', $fileName, 'public');

                                // Store the file path in form data
                                $formData[$fieldName] = $filePath;
                                $fileData[$fieldName] = $filePath;

                                Log::info('File uploaded', ['field' => $fieldName, 'path' => $filePath]);
                            } catch (\Exception $e) {
                                Log::error('File upload failed', [
                                    'field' => $fieldName,
                                    'error' => $e->getMessage()
                                ]);
                                // Continue processing other fields even if one file fails
                            }
                        } else {
                            Log::warning('File field found but no file uploaded', ['field' => $fieldName]);
                        }
                    }
                }
            } else {
                // This is a JSON request
                Log::info('Processing JSON form data');

                if (!$request->has('form_data')) {
                    return response()->json(['error' => 'Form data is required'], 422);
                }

                $formData = $request->form_data;

                // Ensure form_data is an array
                if (!is_array($formData)) {
                    Log::error('form_data is not an array', ['type' => gettype($formData)]);
                    return response()->json(['error' => 'Form data must be an array'], 422);
                }
            }

            // Check if the dynamic table exists, if not create it
            if (!Schema::hasTable($tableName)) {
                Log::info("Creating dynamic table: {$tableName}");

                Schema::create($tableName, function ($table) use ($formStructure) {
                    $table->id();
                    $table->unsignedBigInteger('user_id');

                    // Add columns based on form structure
                    foreach ($formStructure as $field) {
                        if (isset($field['label'])) {
                            $columnName = Str::slug($field['label'], '_');

                            // Determine column type based on field type
                            switch ($field['type']) {
                                case 'textarea':
                                    $table->text($columnName)->nullable();
                                    break;
                                case 'number':
                                    $table->integer($columnName)->nullable();
                                    break;
                                case 'date':
                                    $table->date($columnName)->nullable();
                                    break;
                                case 'checkbox':
                                    $table->boolean($columnName)->default(false);
                                    break;
                                case 'file':
                                    $table->string($columnName, 255)->nullable();
                                    break;
                                case 'email':
                                    $table->string($columnName, 255)->nullable();
                                    break;
                                default:
                                    $table->string($columnName, 255)->nullable();
                                    break;
                            }
                        }
                    }

                    $table->timestamps();

                    // Add foreign key constraint
                    $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
                });

                Log::info("Dynamic table {$tableName} created successfully");
            }

            // We'll use FormSubmission::createSubmission instead of manually inserting data

            // Insert the data into the dynamic table and get the ID
            $submissionId = FormSubmission::createSubmission($formId, Auth::id(), array_merge($formData, $fileData));

            if (!$submissionId) {
                throw new \Exception('Failed to create form submission');
            }

            Log::info("Form data inserted into dynamic table {$tableName}", ['submission_id' => $submissionId]);

            return response()->json([
                'message' => 'Form submitted successfully',
                'submission_id' => $submissionId
            ]);

        } catch (\Exception $e) {
            Log::error('Error submitting form: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());

            return response()->json([
                'error' => 'Failed to submit form: ' . $e->getMessage()
            ], 500);
        }
    }
}
