<?php

namespace App\Http\Controllers\Scientist;

use App\Http\Controllers\Controller;
use App\Models\DistrictStateCoordinator;
use App\Models\ParticipationRequest;
use App\Models\ActionPlan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class DSCInvitationController extends Controller
{
    /**
     * Display the DSC invitation page for a specific action plan.
     */
    public function index($actionPlanId)
    {
        try {
            // Verify the action plan belongs to the current scientist
            $actionPlan = ActionPlan::where('id', $actionPlanId)
                ->where('scientist_id', Auth::id())
                ->first();

            if (!$actionPlan) {
                return redirect()->back()->with('error', 'Action plan not found.');
            }

            return view('scientist.dsc-invitation.index', compact('actionPlan'));
        } catch (\Exception $e) {
            Log::error('Error loading DSC invitation page: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load DSC invitation page.');
        }
    }

    /**
     * Get all available DSCs for invitation.
     */
    public function getAvailableDSCs()
    {
        try {
            // Check if the district_state_coordinators table exists
            if (!Schema::hasTable('district_state_coordinators')) {
                Log::warning('District state coordinators table does not exist');
                return response()->json([]);
            }

            // Get scientist's district
            $scientistEmail = Auth::user()->email;
            $district = \App\Models\StateData::where('scientist', $scientistEmail)->first();

            if (!$district) {
                Log::warning('No district assigned to scientist', [
                    'scientist_id' => Auth::id(),
                    'scientist_email' => $scientistEmail
                ]);
                return response()->json([]);
            }

            // Get DSCs assigned to the scientist's district
            $dscs = DistrictStateCoordinator::whereHas('districts', function($query) use ($district) {
                $query->where('district_id', $district->id);
            })->select(
                'id',
                'name',
                'email',
                'phone_number',
                'designation',
                'expertise_areas',
                'user_id'
            )->get();

            // If no DSCs found for the specific district, try to find DSCs for the same state
            if ($dscs->isEmpty()) {
                Log::info('No DSCs found for district, searching by state', [
                    'district_id' => $district->id,
                    'state' => $district->state
                ]);

                // Get all districts in the same state
                $stateDistricts = \App\Models\StateData::where('state', $district->state)
                    ->pluck('id');

                // Get DSCs assigned to any district in the same state
                $dscs = DistrictStateCoordinator::whereHas('districts', function($query) use ($stateDistricts) {
                    $query->whereIn('district_id', $stateDistricts);
                })->select(
                    'id',
                    'name',
                    'email',
                    'phone_number',
                    'designation',
                    'expertise_areas',
                    'user_id'
                )->get();
            }

            Log::info('Available DSCs retrieved for scientist district', [
                'count' => $dscs->count(),
                'scientist_id' => Auth::id(),
                'district_id' => $district->id,
                'district_name' => $district->district,
                'state' => $district->state
            ]);

            return response()->json($dscs);
        } catch (\Exception $e) {
            Log::error('Error getting available DSCs: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get available DSCs'], 500);
        }
    }

    /**
     * Send invitation to selected DSCs.
     */
    public function sendInvitations(Request $request)
    {
        $request->validate([
            'action_plan_id' => 'required|exists:action_plans,id',
            'selected_dscs' => 'required|array|min:1',
            'selected_dscs.*' => 'exists:district_state_coordinators,id',
            'invitation_message' => 'required|string|min:10|max:1000'
        ]);

        try {
            // Verify the action plan belongs to the current scientist
            $actionPlan = ActionPlan::where('id', $request->action_plan_id)
                ->where('scientist_id', Auth::id())
                ->first();

            if (!$actionPlan) {
                return response()->json(['error' => 'Action plan not found'], 404);
            }

            $successCount = 0;
            $errors = [];

            foreach ($request->selected_dscs as $dscId) {
                try {
                    // Get the DSC details
                    $dsc = DistrictStateCoordinator::find($dscId);
                    
                    if (!$dsc || !$dsc->user_id) {
                        $errors[] = "DSC with ID {$dscId} not found or has no user account";
                        continue;
                    }

                    // Check if invitation already exists
                    $existingRequest = ParticipationRequest::where('scientist_id', Auth::id())
                        ->where('coordinator_id', $dsc->user_id)
                        ->where('action_plan_id', $actionPlan->id)
                        ->first();

                    if ($existingRequest) {
                        $errors[] = "Invitation already sent to {$dsc->name}";
                        continue;
                    }

                    // Create participation request
                    ParticipationRequest::create([
                        'scientist_id' => Auth::id(),
                        'coordinator_id' => $dsc->user_id,
                        'action_plan_id' => $actionPlan->id,
                        'status' => 'pending',
                        'request_message' => $request->invitation_message,
                    ]);

                    $successCount++;

                    Log::info('DSC invitation sent successfully', [
                        'scientist_id' => Auth::id(),
                        'dsc_id' => $dscId,
                        'dsc_name' => $dsc->name,
                        'action_plan_id' => $actionPlan->id
                    ]);

                } catch (\Exception $e) {
                    $errors[] = "Failed to send invitation to DSC ID {$dscId}: " . $e->getMessage();
                    Log::error('Error sending DSC invitation', [
                        'dsc_id' => $dscId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Update action plan with selected coordinators
            $currentCoordinators = $actionPlan->required_coordinators ?? [];
            $newCoordinators = array_unique(array_merge($currentCoordinators, $request->selected_dscs));
            
            $actionPlan->update([
                'required_coordinators' => $newCoordinators
            ]);

            $message = "Successfully sent {$successCount} invitation(s)";
            if (!empty($errors)) {
                $message .= ". Errors: " . implode(', ', $errors);
            }

            return response()->json([
                'message' => $message,
                'success_count' => $successCount,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            Log::error('Error sending DSC invitations: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to send invitations'], 500);
        }
    }

    /**
     * Get invited DSCs for a specific action plan.
     */
    public function getInvitedDSCs($actionPlanId)
    {
        try {
            // Verify the action plan belongs to the current scientist
            $actionPlan = ActionPlan::where('id', $actionPlanId)
                ->where('scientist_id', Auth::id())
                ->first();

            if (!$actionPlan) {
                return response()->json(['error' => 'Action plan not found'], 404);
            }

            // Get participation requests for this action plan
            $invitations = ParticipationRequest::where('action_plan_id', $actionPlanId)
                ->where('scientist_id', Auth::id())
                ->with(['coordinator:id,name,email'])
                ->get();

            return response()->json($invitations);

        } catch (\Exception $e) {
            Log::error('Error getting invited DSCs: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get invited DSCs'], 500);
        }
    }
}
