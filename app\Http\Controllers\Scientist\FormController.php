<?php

namespace App\Http\Controllers\Scientist;

use App\Http\Controllers\Controller;
use App\Models\FormBuilder;
use App\Models\FormSubmission;
use App\Models\StateData;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

class FormController extends Controller
{
    /**
     * Display the forms page.
     */
    public function index()
    {
        return view('scientist.forms.index');
    }

    /**
     * Get forms available to the scientist based on their district status.
     */
    public function getForms()
    {
        try {
            // Get the current scientist's email
            $scientistEmail = Auth::user()->email;

            // Get the scientist's district status
            $districtStatus = StateData::where('scientist', $scientistEmail)
                ->pluck('status')
                ->first();

            // Query forms based on district status
            $query = FormBuilder::query();

            if ($districtStatus === 'Pre-Cocoon') {
                $query->where(function($q) {
                    $q->where('target_user', 'all_scientists')
                      ->orWhere('target_user', 'pre_cocoon');
                });
            } elseif ($districtStatus === 'Post-Cocoon') {
                $query->where(function($q) {
                    $q->where('target_user', 'all_scientists')
                      ->orWhere('target_user', 'post_cocoon');
                });
            } else {
                // If no district status, only show forms for all scientists
                $query->where('target_user', 'all_scientists');
            }

            $forms = $query->get();

            // Check if the scientist has already submitted each form
            foreach ($forms as $form) {
                $tableName = 'form_' . Str::slug($form->form_name, '_');

                // Check if the form is past its deadline
                $form->is_past_deadline = $form->last_date && now() > $form->last_date;

                // Check if the form-specific table exists
                if (Schema::hasTable($tableName)) {
                    // Check if the scientist has already submitted this form
                    $hasSubmitted = DB::table($tableName)
                        ->where('user_id', Auth::id())
                        ->exists();

                    $form->already_submitted = $hasSubmitted;

                    // If the form has been edited after the scientist's submission, allow resubmission
                    if ($hasSubmitted) {
                        $submission = DB::table($tableName)
                            ->where('user_id', Auth::id())
                            ->first();

                        if ($submission && $form->last_edited_at) {
                            $submissionDate = new \DateTime($submission->updated_at);
                            $editDate = new \DateTime($form->last_edited_at);

                            // If the form was edited after the submission, allow resubmission
                            // But only if the form is not past its deadline
                            $form->can_resubmit = ($editDate > $submissionDate) && !$form->is_past_deadline;
                        } else {
                            $form->can_resubmit = false;
                        }
                    } else {
                        // Can submit only if not past deadline
                        $form->can_resubmit = !$form->is_past_deadline;
                    }
                } else {
                    $form->already_submitted = false;
                    // Can submit only if not past deadline
                    $form->can_resubmit = !$form->is_past_deadline;
                }
            }

            return response()->json($forms);
        } catch (\Exception $e) {
            Log::error('Error getting forms: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get forms'], 500);
        }
    }

    /**
     * Get form details.
     */
    public function getFormDetails($id)
    {
        try {
            $form = FormBuilder::findOrFail($id);
            return response()->json($form);
        } catch (\Exception $e) {
            Log::error('Error getting form details: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get form details'], 500);
        }
    }

    /**
     * Display the form for filling/reading.
     */
    public function read($id)
    {
        try {
            // Get the form details
            $form = FormBuilder::findOrFail($id);

            // Check if the scientist has access to this form
            $scientistEmail = Auth::user()->email;
            $districtStatus = StateData::where('scientist', $scientistEmail)
                ->pluck('status')
                ->first();

            // Check if the scientist can access this form based on target_user
            $canAccess = false;
            if ($form->target_user === 'all_scientists') {
                $canAccess = true;
            } elseif ($form->target_user === 'pre_cocoon' && $districtStatus === 'Pre-Cocoon') {
                $canAccess = true;
            } elseif ($form->target_user === 'post_cocoon' && $districtStatus === 'Post-Cocoon') {
                $canAccess = true;
            }

            if (!$canAccess) {
                return redirect()->route('scientist.forms')
                    ->with('error', 'You do not have access to this form.');
            }

            // Check if the form is past its deadline
            if ($form->last_date && now() > $form->last_date) {
                return redirect()->route('scientist.forms')
                    ->with('error', 'This form is no longer accepting submissions. The deadline has passed.');
            }

            // Check if the scientist has already submitted this form
            $tableName = 'form_' . Str::slug($form->form_name, '_');
            $hasSubmitted = false;
            $canResubmit = false;

            if (Schema::hasTable($tableName)) {
                $submission = DB::table($tableName)
                    ->where('user_id', Auth::id())
                    ->first();

                if ($submission) {
                    $hasSubmitted = true;

                    // Check if the form was edited after the submission
                    if ($form->last_edited_at) {
                        $submissionDate = new \DateTime($submission->updated_at);
                        $editDate = new \DateTime($form->last_edited_at);
                        $canResubmit = $editDate > $submissionDate;
                    }
                }
            }

            // If already submitted and cannot resubmit, redirect with message
            if ($hasSubmitted && !$canResubmit) {
                return redirect()->route('scientist.forms')
                    ->with('info', 'You have already submitted this form.');
            }

            // Pass the form data to the view
            return view('formBuilder.read', [
                'form' => $form,
                'hasSubmitted' => $hasSubmitted,
                'canResubmit' => $canResubmit
            ]);
        } catch (\Exception $e) {
            Log::error('Error loading form for reading: ' . $e->getMessage(), [
                'form_id' => $id,
                'user_id' => Auth::id(),
                'exception' => $e
            ]);
            return redirect()->route('scientist.forms')
                ->with('error', 'Failed to load the form. Please try again.');
        }
    }
}
