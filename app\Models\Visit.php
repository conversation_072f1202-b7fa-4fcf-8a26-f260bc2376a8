<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Visit extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'zonal_coordinator_id',
        'district_id',
        'title',
        'location',
        'visit_date',
        'type',
        'purpose',
        'observations',
        'recommendations',
        'photos',
        'gis_location',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'visit_date' => 'datetime',
        'photos' => 'array',
    ];

    /**
     * Get the zonal coordinator associated with this visit.
     */
    public function zonalCoordinator()
    {
        return $this->belongsTo(ZonalCoordinator::class);
    }

    /**
     * Get the district associated with this visit.
     */
    public function district()
    {
        return $this->belongsTo(StateData::class, 'district_id');
    }
}
